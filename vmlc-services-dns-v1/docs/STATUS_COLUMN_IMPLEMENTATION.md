# Status Column Implementation for Jira Integration

## Overview

Added a new "status" column to the Jira ticket update that shows whether the overall DNS job succeeded or failed. This provides immediate visibility into job outcomes directly in the Jira ticket.

## Implementation Details

### 1. Status Tracking in Tasks

**File**: `manage-dns/tasks/dns.yml`

```yaml
# After DNS script execution
- name: Set DNS operation status based on script execution
  ansible.builtin.set_fact:
    dns_operation_status: "{{ 'SUCCESS' if dns_script_output.failed is not defined or not dns_script_output.failed else 'FAILED' }}"
  run_once: true
```

### 2. Status Determination in Handler

**File**: `manage-dns/handlers/update-sr-uat.yml`

```yaml
# Use the status set by the main tasks
- name: Determine overall job status
  ansible.builtin.set_fact:
    overall_job_status: "{{ dns_operation_status | default('UNKNOWN') }}"
  run_once: true
```

### 3. Enhanced Jira API Call

**File**: `manage-dns/handlers/update-sr-uat.yml`

```yaml
# Updated Jira body with status column
body: |
  {
    "rows":[
      {
        "rowId":"{{ var_row_id }}",
        "columns":{
          "remark": "{{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/",
          "status": "{{ overall_job_status }}"
        }
      }
    ]
  }
```

## Status Values

| Status | Description | When Set |
|--------|-------------|----------|
| `SUCCESS` | DNS operation completed successfully | When `dns_script_output.failed` is undefined or false |
| `FAILED` | DNS operation encountered errors | When `dns_script_output.failed` is true |
| `UNKNOWN` | Status could not be determined | When `dns_operation_status` is not set |

## How It Works

### 1. Status Detection Logic
```yaml
dns_operation_status: "{{ 'SUCCESS' if dns_script_output.failed is not defined or not dns_script_output.failed else 'FAILED' }}"
```

This logic:
- Checks if the DNS script execution failed
- Sets `SUCCESS` if no failure detected
- Sets `FAILED` if failure detected

### 2. Handler Integration
- Handler receives the status from the main tasks
- Uses `dns_operation_status` variable set during task execution
- Falls back to `UNKNOWN` if status not available

### 3. Jira Update
- Status appears in the "status" column of the Jira grid
- Provides immediate visual feedback on job outcome
- Complements the existing "remark" column with AAP job link

## Example Jira Updates

### Successful Job
```json
{
  "columns": {
    "remark": "https://aap.hcc.com.sg/#/jobs/playbook/12345/",
    "status": "SUCCESS"
  }
}
```

### Failed Job
```json
{
  "columns": {
    "remark": "https://aap.hcc.com.sg/#/jobs/playbook/12345/",
    "status": "FAILED"
  }
}
```

## Benefits

1. **Immediate Visibility**: Status visible at a glance in Jira
2. **No Additional Clicks**: Don't need to open AAP to see if job succeeded
3. **Audit Trail**: Historical record of job outcomes in Jira
4. **Integration**: Works with existing Jira workflow and notifications
5. **Reliability**: Status determined from actual script execution results

## Testing Scenarios

### Test 1: Successful DNS Operation
```bash
# Expected Result:
# - DNS script executes successfully
# - dns_operation_status = "SUCCESS"
# - Jira status column = "SUCCESS"
```

### Test 2: Failed DNS Operation
```bash
# Expected Result:
# - DNS script fails (e.g., invalid domain)
# - dns_operation_status = "FAILED"
# - Jira status column = "FAILED"
```

### Test 3: Manual AAP Execution (No Jira Update)
```bash
# Expected Result:
# - DNS operation completes
# - Status tracked internally
# - No Jira update attempted (missing variables)
```

## Troubleshooting

### Status Shows "UNKNOWN"
- Check if `dns_operation_status` is being set in tasks
- Verify DNS script execution is registering properly
- Look for task execution errors

### Status Not Appearing in Jira
- Verify Jira grid has a "status" column configured
- Check Jira API permissions for grid updates
- Confirm `var_row_id` points to correct grid row

### Incorrect Status Values
- Review DNS script output and error handling
- Check `ignore_errors: true` settings
- Verify status logic in set_fact task

## Implementation Notes

- Status is determined immediately after DNS script execution
- Uses Ansible's built-in failure detection mechanisms
- Compatible with existing error handling (`ignore_errors: true`)
- Works for both Jira-triggered and manual executions
- Status tracking is independent of Jira update success/failure
