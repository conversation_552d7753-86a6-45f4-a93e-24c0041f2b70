# Environment Variable Approach for Jira Integration

## Overview

The Jira handler now uses a global `var_environment` variable to dynamically select the appropriate vars file. This provides explicit control over which environment configuration is used.

## Implementation

### Environment Variable Logic
```yaml
- name: Determine vars file based on environment
  ansible.builtin.set_fact:
    jira_vars_file: >-
      {{
        '../vars/prod_vars.yml' if var_environment|lower == 'prd' else
        '../vars/dev_vars.yml' if var_environment|lower == 'dev' else
        '../vars/uat_vars.yml'
      }}
```

### Environment Mapping

| `var_environment` Value | Vars File Used | Jira Environment |
|------------------------|----------------|------------------|
| `prd` or `PRD` | `prod_vars.yml` | Production ITSM |
| `dev` or `DEV` | `dev_vars.yml` | UAT Jira (Dev uses UAT) |
| `uat` or `UAT` | `uat_vars.yml` | UAT Jira |
| **Default** (any other value) | `uat_vars.yml` | UAT Jira |

## Environment-Specific Configurations

### Production (`prod_vars.yml`)
```yaml
aap_url: https://aap.hcc.com.sg/
grid_url: "https://itsm.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
jira_grid: !vault |
    # Production Jira credentials
```

### Development (`dev_vars.yml`)
```yaml
aap_url: https://aap.hcc.com.sg/
grid_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
jira_grid: !vault |
    # UAT Jira credentials (dev uses UAT instance)
```

### UAT (`uat_vars.yml`)
```yaml
aap_url: https://aap.hcc.com.sg/
grid_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
jira_grid: !vault |
    # UAT Jira credentials
```

## Usage Examples

### Production Execution
```yaml
# Job Template Variables
var_environment: "prd"
var_sr_number: "SCR-12345"
var_grid_id: "67890"
var_row_id: "11111"

# Result: Uses prod_vars.yml → Production ITSM
```

### Development Execution
```yaml
# Job Template Variables
var_environment: "dev"
var_sr_number: "SCR-12345"
var_grid_id: "67890"
var_row_id: "11111"

# Result: Uses dev_vars.yml → UAT Jira (safe for development)
```

### UAT Execution
```yaml
# Job Template Variables
var_environment: "uat"
var_sr_number: "SCR-12345"
var_grid_id: "67890"
var_row_id: "11111"

# Result: Uses uat_vars.yml → UAT Jira
```

### Manual Execution (No Environment)
```yaml
# Job Template Variables (minimal)
hostname: "testserver"
domain: "healthgrp.com.sg"
var_action: "verify"
# var_environment not provided

# Result: Uses uat_vars.yml (default) → UAT Jira
```

## Debug Output

### Expected Debug Messages
```
TASK [Display selected environment and vars file]
ok: [localhost] => {
    "msg": [
        "Environment: prd",
        "Selected vars file: ../vars/prod_vars.yml"
    ]
}
```

### Environment Detection
```
Environment: prd        → Production
Environment: dev        → Development  
Environment: uat        → UAT
Environment: NOT_SET    → Default (UAT)
```

## Benefits

### 1. **Explicit Control**
- Clear environment specification via `var_environment`
- No guessing or automatic detection
- Consistent behavior across all executions

### 2. **Safe Defaults**
- Defaults to UAT environment if not specified
- Development uses UAT Jira (safe for testing)
- Production requires explicit `var_environment: prd`

### 3. **Clear Debugging**
- Shows selected environment and vars file
- Easy to verify correct configuration is loaded
- Transparent environment selection

### 4. **Flexible Configuration**
- Case-insensitive environment values
- Easy to add new environments
- Centralized environment logic

## Job Template Configuration

### Production Job Template
```yaml
# Extra Variables
var_environment: "prd"
var_sr_number: "{{ sr_number }}"
var_grid_id: "{{ grid_id }}"
var_row_id: "{{ row_id }}"
hostname: "{{ hostname }}"
domain: "{{ domain }}"
var_action: "{{ action }}"
```

### Development Job Template
```yaml
# Extra Variables
var_environment: "dev"
hostname: "{{ hostname }}"
domain: "{{ domain }}"
var_action: "{{ action }}"
# Jira variables optional for dev
```

### UAT Job Template
```yaml
# Extra Variables
var_environment: "uat"
var_sr_number: "{{ sr_number }}"
var_grid_id: "{{ grid_id }}"
var_row_id: "{{ row_id }}"
hostname: "{{ hostname }}"
domain: "{{ domain }}"
var_action: "{{ action }}"
```

## Troubleshooting

### Issue: Wrong environment being used
**Check the debug output:**
```
"Environment: prd"                    # Should match your intention
"Selected vars file: ../vars/prod_vars.yml"  # Should match environment
```

### Issue: Environment not recognized
**Verify variable spelling:**
```yaml
var_environment: "prd"  # ✅ Correct
var_environment: "prod" # ❌ Will default to UAT
```

### Issue: Jira update still failing
**After environment selection:**
1. Verify the selected vars file exists
2. Check Jira credentials in the vars file
3. Confirm grid/row IDs are correct for that environment

## Migration Guide

### For Existing Job Templates
1. **Add `var_environment` variable** to job template extra variables
2. **Set appropriate value**: `prd`, `dev`, or `uat`
3. **Test with UAT first** to verify functionality
4. **Deploy to production** with `var_environment: prd`

### For New Job Templates
1. **Always specify `var_environment`** in extra variables
2. **Use `dev` for development/testing**
3. **Use `uat` for user acceptance testing**
4. **Use `prd` for production deployments**

## Security Considerations

- **Development uses UAT Jira**: Prevents accidental production updates during development
- **Explicit production flag**: Requires intentional `var_environment: prd` for production
- **Vault-encrypted credentials**: All environment credentials are vault-encrypted
- **Environment isolation**: Each environment has separate Jira instances and credentials

This approach provides clear, explicit control over environment selection while maintaining safe defaults and comprehensive debugging capabilities.
