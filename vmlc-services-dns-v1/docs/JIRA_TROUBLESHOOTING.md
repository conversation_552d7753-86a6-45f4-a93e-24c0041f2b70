# Jira Integration Troubleshooting Guide

## Issue: Handler Shows Success but Jira Grid Not Updated

### Enhanced Debugging Added

I've added comprehensive debugging to help identify the issue. The enhanced handler now provides:

1. **Pre-API Call Debug**: Shows all variables before making the API call
2. **API Response Capture**: Captures the actual Jira API response
3. **Post-API Call Debug**: Shows response details and any errors

### Common Causes and Solutions

#### 1. **Missing or Incorrect Variables**

**Symptoms:**
- Debug shows "NOT_SET" for key variables
- Hand<PERSON> runs but variables are undefined

**Check These Variables:**
```yaml
# Required Jira Variables
var_sr_number: "SCR-12345"    # Must be actual SR number, not default
var_grid_id: "12345"          # Jira grid ID
var_row_id: "67890"           # Specific row ID in the grid

# AAP Variables  
aap_url: "https://aap.hcc.com.sg"
tower_job_id: "12345"         # Current job ID
```

**Solution:**
- Verify <PERSON><PERSON> is passing all required variables
- Check variable names match exactly
- Ensure variables are not using default values

#### 2. **Incorrect Jira Grid Configuration**

**Symptoms:**
- API returns 200/204 but grid not updated
- Response shows success but no visible changes

**Common Issues:**
- Wrong `var_grid_id` (pointing to different grid)
- Wrong `var_row_id` (row doesn't exist or wrong row)
- Grid columns don't match ("remark", "status")

**Solution:**
```bash
# Verify grid configuration in Jira
1. Check grid ID in Jira URL
2. Verify row ID exists in the grid
3. Confirm column names: "remark" and "status"
4. Check grid permissions for the service account
```

#### 3. **Authentication Issues**

**Symptoms:**
- 401/403 HTTP status codes
- "Authorization Header: NOT_SET" in debug

**Solution:**
```yaml
# Check uat_vars.yml has correct credentials
jira_grid: "Bearer <token>" # or "Basic <encoded>"
grid_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
```

#### 4. **API Endpoint Issues**

**Symptoms:**
- 404 HTTP status codes
- URL looks incorrect in debug output

**Check:**
- Grid URL format: `{grid_url}/{var_grid_id}/issue/{var_sr_number}/`
- Environment (UAT vs PROD) URLs match
- Grid API endpoint is accessible

#### 5. **JSON Payload Issues**

**Symptoms:**
- 400 HTTP status codes
- JSON parsing errors in response

**Common Issues:**
- Column names don't match Jira grid schema
- Invalid JSON structure
- Special characters in values

### Debugging Steps

#### Step 1: Check Handler Execution
```bash
# Look for these log entries:
"=== Jira API Call Debug Information ==="
"Jira Update Status: ENABLED"
```

#### Step 2: Verify Variables
```bash
# Check debug output for:
- Grid ID: Should be numeric
- SR Number: Should be actual ticket (SCR-XXXXX)
- Row ID: Should be numeric
- AAP URL: Should be set
- Tower Job ID: Should be current job ID
```

#### Step 3: Check API Response
```bash
# Look for:
"=== Jira API Response ==="
"Status Code: 200" or "Status Code: 204"
"Failed: false"
```

#### Step 4: Manual API Test
```bash
# Test the API call manually:
curl -X PUT \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "rows":[{
      "rowId":"<row_id>",
      "columns":{
        "remark":"Test Update",
        "status":"SUCCESS"
      }
    }]
  }' \
  "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid/<grid_id>/issue/<sr_number>/"
```

### Quick Fixes to Try

#### Fix 1: Add Default AAP URL
```yaml
# In uat_vars.yml or dns_vars.yml
aap_url: "https://aap.hcc.com.sg"
```

#### Fix 2: Verify Column Names
```yaml
# Check if Jira grid uses different column names
"columns":{
  "remarks": "...",     # Instead of "remark"
  "job_status": "..."   # Instead of "status"
}
```

#### Fix 3: Test with Minimal Payload
```yaml
# Try updating only one column first
"columns":{
  "remark": "Test - {{ tower_job_id }}"
}
```

### Expected Debug Output (Success)

```
=== Jira API Call Debug Information ===
URL: https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid/12345/issue/SCR-67890/
Grid ID: 12345
SR Number: SCR-67890
Row ID: 98765
AAP URL: https://aap.hcc.com.sg
Tower Job ID: 54321
Job Status: SUCCESS
Authorization Header: SET

=== Jira API Response ===
Status Code: 204
Response Body: 
Failed: false
Error Message: None
```

### Next Steps

1. **Run the playbook** with enhanced debugging
2. **Check the debug output** for the specific values
3. **Compare with this guide** to identify the issue
4. **Test manually** if needed to isolate the problem

The enhanced debugging will show exactly what's being sent to Jira and what response is received, making it much easier to identify the root cause.
