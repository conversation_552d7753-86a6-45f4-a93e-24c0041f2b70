# Vars File Path Fix Documentation

## Issue Description

The Jira handler was failing to load `prod_vars.yml` because `include_vars` in handlers looks for files relative to the handler's directory, not the vars directory.

**Error:**
```
Could not find or access 'prod_vars.yml'
Searched in:
    /runner/project/vmlc-services-dns-v1/manage-dns/handlers/vars/prod_vars.yml
    /runner/project/vmlc-services-dns-v1/manage-dns/handlers/prod_vars.yml
```

## Root Cause

### File Structure
```
vmlc-services-dns-v1/
├── manage-dns/
│   ├── handlers/
│   │   └── update-sr-uat.yml    # Handler file
│   └── vars/
│       ├── prod_vars.yml        # Production vars
│       └── uat_vars.yml         # UAT vars
```

### Original Code (Incorrect)
```yaml
# In handlers/update-sr-uat.yml
- name: Try to include prod_vars.yml first
  ansible.builtin.include_vars: prod_vars.yml  # ❌ Wrong path
```

This looks for `prod_vars.yml` in the `handlers/` directory, but the file is in `vars/`.

## Solution Implemented

### Fixed Path References
```yaml
# In handlers/update-sr-uat.yml
- name: Try to include prod_vars.yml first
  ansible.builtin.include_vars: ../vars/prod_vars.yml  # ✅ Correct relative path
  ignore_errors: true
  register: prod_vars_result
  when: jira_update_enabled

- name: Include uat_vars.yml if prod_vars.yml failed
  ansible.builtin.include_vars: ../vars/uat_vars.yml   # ✅ Correct relative path
  when: jira_update_enabled and prod_vars_result.failed
```

### Path Explanation
- `../vars/prod_vars.yml` means:
  - `..` = Go up one directory (from `handlers/` to `manage-dns/`)
  - `vars/` = Enter the vars directory
  - `prod_vars.yml` = The target file

## Alternative Solutions

### Option 1: Absolute Path (Not Recommended)
```yaml
- name: Include vars with absolute path
  ansible.builtin.include_vars: "{{ playbook_dir }}/manage-dns/vars/prod_vars.yml"
```

**Pros:** Always works regardless of current directory
**Cons:** Less portable, harder to maintain

### Option 2: Playbook-Level vars_files (Recommended for Future)
```yaml
# In main.yml
- name: DNS Management
  hosts: all
  vars_files:
    - manage-dns/vars/dns_vars.yml
    - manage-dns/vars/prod_vars.yml
    - manage-dns/vars/uat_vars.yml
```

**Pros:** Loads all vars at playbook start, available everywhere
**Cons:** Loads both environments (but later ones override earlier ones)

### Option 3: Environment Detection in vars_files
```yaml
# In main.yml
vars_files:
  - manage-dns/vars/dns_vars.yml
  - "manage-dns/vars/{{ 'prod' if (tower_job_template_name is defined and 'prod' in tower_job_template_name) else 'uat' }}_vars.yml"
```

**Pros:** Dynamic environment selection
**Cons:** Requires reliable environment detection

## Current Implementation Benefits

### 1. **Robust Fallback**
- Tries production first (most common in real deployments)
- Falls back to UAT if production not available
- Clear logging of which file was loaded

### 2. **Path Correctness**
- Uses correct relative paths (`../vars/`)
- Works regardless of execution context
- Maintains file organization

### 3. **Error Handling**
- `ignore_errors: true` prevents failures
- Graceful fallback mechanism
- Clear debug output

## Testing the Fix

### Expected Debug Output (Success)
```
TASK [Display which vars file was loaded] 
ok: [localhost] => {
    "msg": "Loaded Jira vars from: prod_vars.yml"
}
```

### Expected Debug Output (Fallback)
```
TASK [Display which vars file was loaded] 
ok: [localhost] => {
    "msg": "Loaded Jira vars from: uat_vars.yml"
}
```

### File Loading Verification
```yaml
# Check if variables are loaded correctly
- name: Verify vars loaded
  ansible.builtin.debug:
    msg:
      - "Grid URL: {{ grid_url | default('NOT_LOADED') }}"
      - "AAP URL: {{ aap_url | default('NOT_LOADED') }}"
      - "Jira Grid Token: {{ 'LOADED' if jira_grid is defined else 'NOT_LOADED' }}"
```

## Troubleshooting

### Issue: Still getting "Could not find" error
**Check:**
1. File permissions on vars files
2. Vault encryption status
3. Relative path correctness

**Debug:**
```bash
# From the handlers directory, verify path
ls -la ../vars/
# Should show prod_vars.yml and uat_vars.yml
```

### Issue: Wrong environment vars loaded
**Check the debug output:**
```
"Loaded Jira vars from: prod_vars.yml"  # Production loaded
"Loaded Jira vars from: uat_vars.yml"   # UAT loaded
```

### Issue: Variables not available in handler
**Verify vars are loaded:**
```yaml
- name: Debug loaded variables
  ansible.builtin.debug:
    var: grid_url
  when: jira_update_enabled
```

## Files Modified

### v1 Changes
- ✅ `vmlc-services-dns-v1/manage-dns/handlers/update-sr-uat.yml`
  - Fixed path: `../vars/prod_vars.yml`
  - Fixed path: `../vars/uat_vars.yml`

### v2 Changes  
- ✅ `vmlc-services-dns-v2/manage-dns/handlers/update-sr-uat.yml`
  - Fixed path: `../vars/prod_vars.yml`
  - Fixed path: `../vars/uat_vars.yml`

## Implementation Notes

- Uses relative paths for portability
- Maintains fallback logic for robustness
- Compatible with both AAP and local execution
- Works with vault-encrypted vars files
- Preserves all existing functionality

This fix ensures that the correct vars files are loaded regardless of the execution environment, resolving the "Could not find prod_vars.yml" error.
