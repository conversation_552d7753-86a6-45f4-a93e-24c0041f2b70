# Jira Integration Fix Documentation

## Issue Description

The Jira handler was not being triggered after DNS operations completion because:

1. **Missing notify statements**: No tasks were notifying the "Update Jira Ticket" handler
2. **No conditional logic**: The handler would fail when run manually from AAP without Jira variables

## Root Cause Analysis

### Original Problem
- Handler defined in `main.yml` but never triggered
- No distinction between Jira-triggered jobs vs manual AAP execution
- Missing required variables (`var_sr_number`, `var_grid_id`, `var_row_id`) would cause failures

### Variables Required for Jira Integration
- `var_sr_number`: Service Request number (e.g., "SCR-12345")
- `var_grid_id`: Jira grid identifier
- `var_row_id`: Specific row ID in the Jira grid
- `aap_url`: AAP base URL for job links
- `tower_job_id`: Current job ID for linking

## Solution Implemented

### 1. Added Notify Statements
**File**: `manage-dns/tasks/dns.yml`

```yaml
# Primary trigger - Email notification task
- name: Triggering notifications to relevant teams
  community.general.mail:
    # ... email configuration ...
  notify: Update Jira Ticket

# Fallback trigger - Ensures handler runs even if email fails
- name: Trigger Jira ticket update after DNS operations completion
  ansible.builtin.debug:
    msg: "DNS operations completed. Triggering Jira ticket update for {{ var_ticket | default('SCR-XXXXX') }}"
  run_once: true
  notify: Update Jira Ticket
  when: 
    - var_sr_number is defined 
    - var_sr_number != ""
    - var_sr_number != "SCR-XXXXX"
    - var_grid_id is defined
    - var_row_id is defined
```

### 2. Enhanced Handler Logic
**File**: `manage-dns/handlers/update-sr-uat.yml`

```yaml
# Smart detection of Jira vs Manual execution
- name: Check if all required Jira variables are available
  ansible.builtin.set_fact:
    jira_update_enabled: >-
      {{
        (var_sr_number is defined and var_sr_number != '' and var_sr_number != 'SCR-XXXXX') and
        (var_grid_id is defined and var_grid_id != '') and
        (var_row_id is defined and var_row_id != '')
      }}

# Conditional Jira update - only runs when all variables present
- name: Update grid row in a SR ticket
  ansible.builtin.uri:
    # ... Jira API call ...
  when: jira_update_enabled
```

## How It Works

### Jira-Triggered Execution
1. **Jira provides all required variables**:
   - `var_sr_number`: "SCR-12345"
   - `var_grid_id`: "grid123"
   - `var_row_id`: "row456"

2. **Handler logic**:
   - `jira_update_enabled` = `true`
   - Jira API call executes
   - Ticket updated with AAP job link

### Manual AAP Execution
1. **Missing or default Jira variables**:
   - `var_sr_number`: "SCR-XXXXX" (default) or undefined
   - `var_grid_id`: undefined
   - `var_row_id`: undefined

2. **Handler logic**:
   - `jira_update_enabled` = `false`
   - Jira API call skipped
   - No failure, job completes successfully

## Testing Scenarios

### Test 1: Jira-Triggered Job
```bash
# Variables provided by Jira
var_sr_number: "SCR-12345"
var_grid_id: "12345"
var_row_id: "67890"

# Expected Result: Jira ticket updated
```

### Test 2: Manual AAP Execution
```bash
# No Jira variables provided or defaults used
var_sr_number: "SCR-XXXXX"  # or undefined

# Expected Result: Job completes, no Jira update attempted
```

### Test 3: Partial Jira Variables
```bash
# Only some variables provided
var_sr_number: "SCR-12345"
# var_grid_id and var_row_id missing

# Expected Result: Job completes, no Jira update attempted
```

## Benefits

1. **Robust Operation**: Works for both Jira and manual execution
2. **No Failures**: Missing variables don't cause job failures
3. **Clear Logging**: Debug messages show Jira update status
4. **Backward Compatible**: Existing functionality preserved
5. **Fail-Safe**: Multiple trigger points ensure handler execution

## Monitoring and Troubleshooting

### Log Messages to Look For
```
"Jira Update Status: ENABLED" - Jira update will proceed
"Jira Update Status: DISABLED" - Jira update skipped
"DNS operations completed. Triggering Jira ticket update" - Handler triggered
```

### Common Issues
1. **Handler not triggered**: Check for `notify` statements in tasks
2. **Jira update skipped**: Verify all required variables are provided
3. **API failures**: Check Jira credentials and network connectivity

## Implementation Notes

- Uses `ignore_errors: true` for Jira API calls to prevent job failures
- Conditional logic prevents unnecessary API calls
- Debug messages provide clear status information
- Compatible with both UAT and Production Jira environments
