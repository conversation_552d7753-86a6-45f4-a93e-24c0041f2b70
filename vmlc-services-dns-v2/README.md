# Enhanced DNS Management Project v2.0 - Batch Processing (vmlc-services-dns-v2)

## 🚀 NEW in v2.0: Batch Processing Support

This version introduces comprehensive batch processing capabilities that allow you to manage multiple hostnames for a single domain in one execution, providing:

- **Multi-hostname operations**: Process multiple hostnames in a single job
- **Individual error isolation**: One hostname failure doesn't stop others
- **Consolidated reporting**: Single email with all hostname results
- **Unified Jira integration**: One Jira update for the entire batch
- **Backward compatibility**: All v1.0 functionality preserved

### Quick Start - Batch Processing

#### Batch Add Operation
```bash
ansible-playbook main-batch.yml \
  -e "hostname_list='server01,server02,server03'" \
  -e "domain='healthgrp.com.sg'" \
  -e "ip_address_list='*********,*********,*********'" \
  -e "var_action='add'"
```

#### Batch Verify Operation
```bash
ansible-playbook main-batch.yml \
  -e "hostname_list='web01,web02,web03'" \
  -e "domain='hcloud.healthgrp.com.sg'" \
  -e "var_action='verify'"
```

## 1. Overview

This project provides enterprise-grade DNS management capabilities for VM lifecycle operations across multiple domains with intelligent PTR record management. It supports both single hostname operations (v1.0 compatibility) and batch processing of multiple hostnames (v2.0 enhancement). The system allows for the verification, addition, and removal of DNS A records and their corresponding PTR records using advanced zone detection and safety features.

**Author:** CES Operational Excellence Team
**Contributor:** Muhammad Syazani Bin Mohamed Khairi (7409)

## 2. Enhanced Features

### 2.1 Batch Processing (NEW v2.0)
- **Multi-hostname Operations**: Process multiple hostnames for a single domain in one execution
- **Individual Error Isolation**: One hostname failure doesn't stop processing of others
- **Consolidated Reporting**: Single email notification with all hostname results in tabular format
- **Unified Jira Integration**: One Jira update containing batch operation summary
- **Batch Status Tracking**: Overall batch status (SUCCESS/PARTIAL_SUCCESS/FAILED) with individual hostname results
- **Performance Optimization**: Efficient processing without requiring separate job executions

### 2.2 Core DNS Operations
- **DNS A Record Management**: Verify, add, and remove A records (single or batch)
- **Intelligent PTR Record Management**: Advanced PTR management with hierarchical zone detection
- **Multi-Domain Support**: Support for multiple domains with domain-specific DNS servers
- **Special PTR Handling**: Enhanced handling for SingHealth domains with separate PTR servers
- **Backward Compatibility**: Full support for single hostname operations (v1.0 compatibility)

### 2.2 Advanced PTR Management
- **3-Tier Zone Detection**: Intelligent fallback from 3-octet → 2-octet → 1-octet reverse zones
- **Safe PTR Removal**: Only removes PTR records that match the hostname being removed
- **Timeout Protection**: 10-second timeout for DNS queries to prevent script hanging
- **Enhanced Error Handling**: PTR operation failures never impact A record operations
- **Zone Preservation**: Never deletes DNS zones, only manages individual PTR records

### 2.3 Operational Excellence
- **Enhanced Logging**: Color-coded output with timing information and detailed operation status
- **Comprehensive Error Isolation**: Graceful degradation when PTR zones are unavailable
- **Backward Compatibility**: Legacy behavior preserved with optional PTR management
- **Email Notifications**: Enhanced notifications with PTR management status
- **Jira Integration**: Update Jira tickets with comprehensive job details
- **CyberArk Integration**: Secure credential retrieval from CyberArk PAM Safe

## 3. Supported Domains

### 3.1 Development/Staging Environments

- **devhealthgrp.com.sg** - Dev HealthGrp domain
- **healthgrpexts.com.sg** - HealthGrp Exts domain
- **nnstg.local** - NNSTG local domain
- **ses.shsu.com.sg** - SingHealth SES staging domain (special PTR handling)

### 3.2 Production Environments

- **shses.shs.com.sg** - SingHealth SES production domain (special PTR handling)
- **nhg.local** - NHG local domain
- **aic.local** - AIC local domain
- **iltc.healthgrp.com.sg** - ILTC HealthGrp domain
- **healthgrp.com.sg** - HealthGrp domain
- **hcloud.healthgrp.com.sg** - HCloud HealthGrp domain
- **healthgrpextp.com.sg** - HealthGrp ExtP domain

### 3.3 Special Domain Handling

**SingHealth Domains** require special PTR server configuration:
- **ses.shsu.com.sg**: A records → `sedcvssys22h1.ses.shsu.com.sg`, PTR records → `shdcvsys22h1.shsu.com.sg`
- **shses.shs.com.sg**: A records → `sesdcvpsys01.shses.shs.com.sg`, PTR records → `sesdcvpsys11.shs.com.sg`

## 4. Project Structure

```
vmlc-services-dns-v2/
├── main.yml                                # Main playbook entry point (single hostname - v1.0 compatibility)
├── main-batch.yml                          # NEW: Batch processing playbook entry point (v2.0)
├── docs/                                   # Documentation folder
│   ├── USAGE.md                            # Detailed usage instructions
│   ├── STRUCTURE.md                        # Detailed project structure explanation
│   ├── TROUBLESHOOTING.md                  # Troubleshooting guide
│   ├── POWERSHELL_TESTING.md               # Guide for direct PowerShell script testing
│   ├── BATCH_PROCESSING_GUIDE.md           # NEW: Comprehensive batch processing guide
│   └── AAP_EXECUTION_EXAMPLES.md           # NEW: AAP execution examples for batch operations
├── manage-dns/
│   ├── files/
│   │   └── set-dns.ps1                     # Enhanced PowerShell script with batch processing support
│   ├── handlers/
│   │   ├── update-sr-uat.yml               # Handler for updating Jira tickets (single hostname)
│   │   └── update-sr-batch.yml             # NEW: Handler for batch Jira updates
│   ├── tasks/
│   │   ├── main.yml                        # Main tasks entry point (single hostname)
│   │   ├── dns.yml                         # Enhanced DNS management tasks with PTR support
│   │   └── dns-batch.yml                   # NEW: Batch DNS management tasks
│   ├── templates/
│   │   ├── email_template_dns.j2           # Enhanced email template with PTR status
│   │   └── email_template_dns_batch.j2     # NEW: Batch email template with tabular results
│   └── vars/
│       ├── dns_vars.yml                    # Enhanced DNS variables with batch template support
│       ├── prod_vars.yml                   # Production environment variables
│       ├── dev_vars.yml                    # Development environment variables
│       └── uat_vars.yml                    # UAT environment variables
```

For a detailed explanation of the project structure, see [docs/STRUCTURE.md](docs/STRUCTURE.md).

## 5. Prerequisites

- Ansible Automation Platform (AAP)
- Access to DNS servers via ADMT servers
- Service accounts with appropriate permissions
- CyberArk PAM Safe for credential management

## 6. Usage

The project is designed to be executed from Ansible Automation Platform (AAP) using Job Templates. It cannot be run directly from the command line as users and developers do not have access to run ansible-playbook commands directly on servers.

For detailed usage instructions, see [docs/USAGE.md](docs/USAGE.md).

### 6.1 Direct PowerShell Script Testing

For DNS engineers who need to test or use the PowerShell script directly on ADMT servers, a comprehensive guide is available at [docs/POWERSHELL_TESTING.md](docs/POWERSHELL_TESTING.md). This guide includes:

- Step-by-step instructions for running the script
- Examples for different operations (verify, add, remove)
- Testing scenarios and best practices
- Troubleshooting tips

## 7. Batch Processing Operations (NEW v2.0)

### 7.1 Batch Verify Operation

Checks multiple DNS A records and their corresponding PTR records in a single execution:
- **Multi-hostname Verification**: Verifies all hostnames in the batch
- **Individual Status Tracking**: Each hostname gets its own verification result
- **Consolidated Reporting**: Single email with tabular results for all hostnames
- **Error Isolation**: Failed verifications don't stop processing of other hostnames

### 7.2 Batch Add Operation

Adds multiple DNS A records and their corresponding PTR records with intelligent zone management:
- **Multi-hostname Creation**: Creates A records for all hostnames in the batch
- **IP Address Mapping**: Maps each hostname to its corresponding IP address
- **Optimal PTR Zone Selection**: Automatically detects best available PTR zone for each hostname
- **Individual Success Tracking**: Tracks success/failure for each hostname independently

### 7.3 Batch Remove Operation

Removes multiple DNS A records and matching PTR records safely:
- **Multi-hostname Removal**: Removes A records for all hostnames in the batch
- **Safe PTR Removal**: Only removes PTR records that match each hostname exactly
- **Individual Processing**: Each hostname is processed independently
- **Consolidated Results**: Single report showing results for all hostnames

### 7.4 Batch Processing Benefits

- **Efficiency**: Process multiple hostnames in one job execution
- **Consistency**: All hostnames processed with same configuration and credentials
- **Reporting**: Single consolidated email and Jira update for the entire batch
- **Error Handling**: Individual hostname failures don't impact others
- **Performance**: Reduced overhead compared to multiple single hostname jobs

## 8. Enhanced Operations (Single Hostname - v1.0 Compatibility)

### 8.1 Verify DNS Record

Checks if a DNS A record and its corresponding PTR record exist using intelligent zone detection:
- **A Record Verification**: Checks existence and retrieves current IP and TTL
- **Intelligent PTR Detection**: Uses 3-tier zone detection to find PTR records
- **Comprehensive Reporting**: Provides detailed status of both A and PTR records

### 7.2 Add DNS Record

Adds a DNS A record and its corresponding PTR record with intelligent zone management:
- **A Record Creation**: Creates A record with specified IP and TTL
- **Optimal PTR Zone Selection**: Automatically detects best available PTR zone
- **Safe PTR Creation**: Only creates PTR records in detected zones
- **Idempotent Operations**: Safe to run multiple times without side effects

### 7.3 Remove DNS Record

Removes a DNS A record and matching PTR records safely:
- **A Record Removal**: Removes the specified A record
- **Safe PTR Removal**: Only removes PTR records that match the hostname exactly
- **Hostname Verification**: Verifies PTR record points to the hostname being removed
- **Zone Preservation**: Never deletes DNS zones, only individual records

### 7.4 PTR Management Control

All operations support PTR management control:
- **Enabled (default)**: Full intelligent PTR management with zone detection
- **Disabled**: A record operations only, preserving legacy behavior

## 8. Intelligent PTR Zone Detection

### 8.1 How Zone Detection Works

The system uses a sophisticated 3-tier hierarchical approach to find the optimal PTR zone:

1. **3-octet zone** (most specific): `30.20.10.in-addr.arpa` for IP `***********`
2. **2-octet zone** (medium): `20.10.in-addr.arpa` for IP `***********`
3. **1-octet zone** (least specific): `10.in-addr.arpa` for IP `***********`

### 8.2 Zone Detection Features

- **Timeout Protection**: 10-second timeout per zone check prevents hanging
- **Sequential Testing**: Tests zones from most specific to least specific
- **First Match Wins**: Uses the first available zone for optimal specificity
- **Graceful Fallback**: Continues with A record operations if no PTR zones found
- **Timing Metrics**: Provides detection timing for performance monitoring

### 8.3 PTR Record Naming

PTR record names are calculated based on the detected zone:
- **3-octet zone**: Record name = last octet (e.g., "40")
- **2-octet zone**: Record name = last two octets (e.g., "40.30")
- **1-octet zone**: Record name = last three octets (e.g., "40.30.20")

## 9. Security Considerations

- **Encrypted Credentials**: All credentials encrypted using Ansible Vault
- **Script Cleanup**: PowerShell scripts automatically cleaned up after execution
- **Information Masking**: Sensitive information masked in logs using no_log
- **Secure Credential Management**: CyberArk integration for credential retrieval
- **Zone Protection**: Never deletes DNS zones, only manages individual records
- **Hostname Verification**: PTR removal only for matching hostnames

## 10. Idempotent Operations

All DNS operations in this project are designed to be idempotent, ensuring safe and predictable behavior regardless of how many times they are executed:

### 10.1 Verification Operations

- Verification operations are inherently idempotent as they only read data and do not modify any records
- Multiple verification operations on the same record will always produce the same result
- Verification checks both A records and their corresponding PTR records

### 10.2 Enhanced Addition Operations

- **A Record Idempotency**: Checks if A record exists before adding
- **IP Address Handling**: No changes if same IP exists, updates if different IP
- **Intelligent PTR Management**: PTR records only added in detected zones if they don't exist
- **Zone Detection Consistency**: Same zone detection logic applied each time
- **Safe Retry**: Multiple executions produce consistent results without duplicates

### 10.3 Enhanced Removal Operations

- **A Record Verification**: Checks A record existence before removal
- **Safe PTR Removal**: Only removes PTR records that match the hostname exactly
- **Hostname Verification**: Verifies PTR record points to the correct hostname
- **Graceful Handling**: Completes successfully even if records don't exist
- **Zone Safety**: Never removes DNS zones, only individual records

### 10.4 Enhanced Benefits of Idempotency

- **Safety**: Operations can be retried without risk of creating inconsistent or duplicate records
- **Reliability**: Failed operations can be safely retried without manual cleanup
- **Predictability**: The same operation will always produce the same result
- **Auditability**: Each operation generates a detailed report of what was changed (if anything)
- **PTR Safety**: PTR operations are safe and consistent across multiple executions
- **Zone Consistency**: Zone detection produces consistent results for the same IP ranges

## 11. Quick Start Examples

### 11.1 Batch Processing Examples (NEW v2.0)

```yaml
# Batch verify multiple hostnames
hostname_list: "web01,web02,web03,web04"
domain: healthgrp.com.sg
var_action: verify
manage_ptr: true

# Batch add multiple hostnames with IP addresses
hostname_list: "server01,server02,server03"
domain: hcloud.healthgrp.com.sg
ip_address_list: "*********,*********,*********"
var_action: add
manage_ptr: true

# Batch remove multiple hostnames
hostname_list: "old-app01,old-app02,old-app03"
domain: healthgrpexts.com.sg
var_action: remove
manage_ptr: true

# Batch operation with Jira integration
hostname_list: "prod-web01,prod-web02,prod-web03"
domain: healthgrp.com.sg
ip_address_list: "*********,*********,*********"
var_action: add
var_environment: prd
var_sr_number: SCR-12345
var_grid_id: 12345
var_row_id: 67890
```

### 11.2 Single Hostname Operations (v1.0 Compatibility)

```yaml
# Verify DNS record with intelligent PTR detection
var_action: verify
domain: healthgrp.com.sg
hostname: server01
manage_ptr: true

# Add DNS record with automatic PTR zone detection
var_action: add
domain: healthgrp.com.sg
hostname: server01
ipaddress: ***********
manage_ptr: true

# Remove DNS record with safe PTR removal
var_action: remove
domain: healthgrp.com.sg
hostname: server01
manage_ptr: true
```

### 11.2 Legacy Mode (A Records Only)

```yaml
# A record operations only (legacy behavior)
var_action: add
domain: healthgrp.com.sg
hostname: server01
ipaddress: ***********
manage_ptr: false
```

## 12. Troubleshooting

For comprehensive troubleshooting information, see [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md).

## 13. License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
