---
# =========================================================================
# Main Batch DNS Management Playbook v2.0
# =========================================================================
# This playbook orchestrates batch DNS management operations for VM lifecycle
# It handles A record and PTR record operations for multiple hostnames in a single domain
# Operations supported: verify, add, remove
#
# NEW v2.0 Features:
# - Batch processing support for multiple hostnames in single domain
# - Consolidated Jira integration for batch operations
# - Enhanced email reporting with batch results
# - Individual hostname error isolation
# - Backward compatibility with single hostname operations
#
# Input Variables:
# - hostname_list: Comma-separated list of hostnames (e.g., "server01,server02,server03")
# - domain: Single domain for all hostnames (e.g., "healthgrp.com.sg")
# - ip_address_list: Comma-separated list of IP addresses (for add operations)
# - var_action: "verify" | "add" | "remove"
# - var_environment: "prd" | "dev" | "uat" (determines Jira vars file)
#
# Example Usage:
# ansible-playbook main-batch.yml -e "hostname_list='server01,server02,server03' domain='healthgrp.com.sg' var_action='add' ip_address_list='*********,*********,*********'"
#
# Author: CES Operational Excellence Team
# Contributor: Muhammad Syazani Bin Mohamed Khairi (7409)
# Version: 2.0 - Enhanced Batch Processing
# =========================================================================

- name: Batch DNS Management
  hosts: all
  gather_facts: false

  vars_files:
    - manage-dns/vars/dns_vars.yml

  handlers:
    - name: Update Jira Ticket
      ansible.builtin.include_tasks: manage-dns/handlers/update-sr-batch.yml

  pre_tasks:
    - name: Validate batch operation inputs
      ansible.builtin.assert:
        that:
          - hostname_list is defined
          - hostname_list != ""
          - domain is defined
          - domain != ""
          - var_action is defined
          - var_action in ['verify', 'add', 'remove']
        fail_msg: "Required variables missing: hostname_list, domain, and var_action must be provided"
      run_once: true

    - name: Parse hostname list
      ansible.builtin.set_fact:
        parsed_hostnames: "{{ hostname_list.split(',') | map('trim') | list }}"
      run_once: true

    - name: Parse IP address list (if provided)
      ansible.builtin.set_fact:
        parsed_ip_addresses: "{{ ip_address_list.split(',') | map('trim') | list if ip_address_list is defined and ip_address_list != '' else [] }}"
      run_once: true

    - name: Validate IP addresses for add operations
      ansible.builtin.assert:
        that:
          - parsed_ip_addresses | length == parsed_hostnames | length
        fail_msg: "For 'add' operations, the number of IP addresses must match the number of hostnames"
      when: var_action == 'add'
      run_once: true

    - name: Display batch operation summary
      ansible.builtin.debug:
        msg:
          - "=== Batch DNS Operation Summary ==="
          - "Operation: {{ var_action | upper }}"
          - "Domain: {{ domain }}"
          - "Hostnames: {{ parsed_hostnames | join(', ') }}"
          - "Total Hostnames: {{ parsed_hostnames | length }}"
          - "IP Addresses: {{ parsed_ip_addresses | join(', ') if parsed_ip_addresses | length > 0 else 'N/A' }}"
          - "PTR Management: {{ manage_ptr | default(true) }}"
          - "Ticket: {{ var_ticket | default('Manual Execution') }}"
      run_once: true

    - name: Retrieve Password from PAM Safe
      ansible.builtin.include_role:
        name: cloud_cpe.cyberark_ccp.retrieve_from_cyberark

  tasks:
    - name: Include batch DNS tasks
      ansible.builtin.include_tasks: manage-dns/tasks/dns-batch.yml
