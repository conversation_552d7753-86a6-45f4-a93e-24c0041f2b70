---
# =========================================================================
# Main playbook for DNS Management v2 - Multi-Hostname Batch Processing
# =========================================================================
# This playbook orchestrates DNS management operations for multiple hostnames
# in a single domain with one execution. It handles A record and PTR record 
# operations across multiple hostnames efficiently.
# Operations supported: verify, add, remove (applied to all hostnames)
#
# Enhanced Features v2 - Batch Processing:
# - Multiple hostname processing for single domain
# - Dynamic ADMT server selection with AAP Instance Group routing
# - Consolidated logging and reporting for all hostnames
# - Enhanced Jira integration with batch summary
# - Efficient single-job execution for multiple servers
# - Enterprise automation with runtime variables
#
# Input Variables:
#   hostnames: ["server01", "server02", "server03"] (list of hostnames)
#   domain: "healthgrp.com.sg" (single domain for all hostnames)
#   var_action: "add" | "remove" | "verify" (applied to all hostnames)
#   var_environment: "prd" | "dev" | "uat" (determines Jira vars file)
#   ipaddresses: ["********", "********", "********"] (optional, for add operations)
#
# Author: CES Operational Excellence Team
# Contributor: Muhammad Syazani Bin Mohamed Khairi (7409)
# Version: 2.1 - Batch Processing
# =========================================================================

- name: Dynamic ADMT Server Management and Batch DNS Operations
  hosts: localhost
  gather_facts: false
  connection: local

  vars_files:
    - manage-dns/vars/dns_vars.yml

  vars:
    # Runtime variables for enterprise automation scenarios
    var_fqdn: "{{ FQDN | default('') }}"
    var_prd_ip: "{{ PRD_IP | default('') }}"
    var_environment: "{{ Environment | default('uat') }}"
    var_dc_location: "{{ dc_location | default('') }}"
    
    # Batch DNS operation variables
    var_action: "{{ var_action | default('verify') }}"
    var_domain: "{{ domain | default('') }}"
    var_hostnames: "{{ hostnames | default([hostname]) if hostname is defined else [] }}"
    var_ipaddresses: "{{ ipaddresses | default([ipaddress]) if ipaddress is defined else [] }}"
    var_ttl: "{{ ttl | default('3600') }}"
    var_manage_ptr: "{{ manage_ptr | default(true) }}"
    var_sr_number: "{{ var_sr_number | default('SCR-XXXXX') }}"

  pre_tasks:
    - name: Validate batch DNS operation variables
      ansible.builtin.assert:
        that:
          - var_domain != ""
          - var_hostnames | length > 0
          - var_action in ['verify', 'add', 'remove']
          - var_action != 'add' or (var_ipaddresses | length == var_hostnames | length or var_ipaddresses | length == 1)
        fail_msg: |
          Required variables missing or invalid for batch operation:
          - domain: {{ var_domain }}
          - hostnames: {{ var_hostnames }}
          - var_action: {{ var_action }}
          - ipaddresses: {{ var_ipaddresses }} (required for 'add' action, must match hostname count or be single IP)
        success_msg: "Batch DNS operation variables validated successfully for {{ var_hostnames | length }} hostname(s)"
      run_once: true

    - name: Initialize batch processing variables
      ansible.builtin.set_fact:
        batch_results: []
        batch_summary:
          total_hostnames: "{{ var_hostnames | length }}"
          domain: "{{ var_domain }}"
          action: "{{ var_action }}"
          successful_operations: 0
          failed_operations: 0
          start_time: "{{ ansible_date_time.iso8601 }}"
      run_once: true

    - name: Initialize logging for batch DNS operations
      ansible.builtin.lineinfile:
        path: "/tmp/ansible-dns-v2-batch.log"
        line: "{{ ansible_date_time.iso8601 }} - Starting batch DNS v2 operation: {{ var_action }} for {{ var_hostnames | length }} hostname(s) in {{ var_domain }}"
        create: true
        mode: '0644'
      run_once: true

    - name: Display batch operation overview
      ansible.builtin.debug:
        msg:
          - "=== Batch DNS Operation Overview ==="
          - "Domain: {{ var_domain }}"
          - "Action: {{ var_action }}"
          - "Hostnames: {{ var_hostnames | join(', ') }}"
          - "IP Addresses: {{ var_ipaddresses | join(', ') if var_ipaddresses | length > 0 else 'N/A' }}"
          - "Total Count: {{ var_hostnames | length }}"
          - "Environment: {{ var_environment }}"
          - "===================================="
      run_once: true

    - name: Determine target ADMT server based on domain
      ansible.builtin.set_fact:
        selected_admt_server: "{{ admt_servers_map[var_domain] | first }}"
        selected_instance_group: "{{ admt_instance_group_mapping[admt_servers_map[var_domain] | first] | default(default_instance_group) }}"
        derived_dc_location: "{{ 'HDC2' if admt_instance_group_mapping[admt_servers_map[var_domain] | first] | default(default_instance_group) == 'SYP_H_HPC_MGT_UNXWIN_HDC2' else 'HDC1' }}"
      when: 
        - var_domain != ""
        - var_domain in admt_servers_map
      run_once: true

    - name: Validate ADMT server selection for batch operation
      ansible.builtin.assert:
        that:
          - selected_admt_server is defined
          - selected_instance_group is defined
        fail_msg: "Failed to select ADMT server for domain: {{ var_domain }}. Check admt_servers_map configuration."
        success_msg: "ADMT server selected for batch operation: {{ selected_admt_server }} → Instance Group: {{ selected_instance_group }}"
      run_once: true

    - name: Select credentials based on domain for batch operation
      ansible.builtin.set_fact:
        selected_ansible_user: "{{ var_dns_devhealthgrp_username if var_domain == 'devhealthgrp.com.sg' else
          var_dns_healthgrpexts_username if var_domain == 'healthgrpexts.com.sg' else
          var_dns_nnstg_username if var_domain == 'nnstg.local' else
          var_dns_ses_username if var_domain == 'ses.shsu.com.sg' else
          var_dns_shses_username if var_domain == 'shses.shs.com.sg' else
          var_dns_nhg_username if var_domain == 'nhg.local' else
          var_dns_aic_username if var_domain == 'aic.local' else
          var_dns_iltc_username if var_domain == 'iltc.healthgrp.com.sg' else
          var_dns_healthgrp_username if var_domain == 'healthgrp.com.sg' else
          var_dns_hcloud_username if var_domain == 'hcloud.healthgrp.com.sg' else
          var_dns_healthgrpextp_username if var_domain == 'healthgrpextp.com.sg' else '' }}"
        selected_ansible_password: "{{ var_dns_devhealthgrp_password if var_domain == 'devhealthgrp.com.sg' else
          var_dns_healthgrpexts_password if var_domain == 'healthgrpexts.com.sg' else
          var_dns_nnstg_password if var_domain == 'nnstg.local' else
          var_dns_ses_password if var_domain == 'ses.shsu.com.sg' else
          var_dns_shses_password if var_domain == 'shses.shs.com.sg' else
          var_dns_nhg_password if var_domain == 'nhg.local' else
          var_dns_aic_password if var_domain == 'aic.local' else
          var_dns_iltc_password if var_domain == 'iltc.healthgrp.com.sg' else
          var_dns_healthgrp_password if var_domain == 'healthgrp.com.sg' else
          var_dns_hcloud_password if var_domain == 'hcloud.healthgrp.com.sg' else
          var_dns_healthgrpextp_password if var_domain == 'healthgrpextp.com.sg' else '' }}"
      no_log: true
      run_once: true

    - name: Add ADMT server to inventory for batch processing
      ansible.builtin.add_host:
        name: "{{ selected_admt_server }}"
        groups: 
          - "admt_servers"
          - "{{ selected_instance_group }}"
        ansible_host: "{{ selected_admt_server }}"
        ansible_user: "{{ selected_ansible_user }}"
        ansible_password: "{{ selected_ansible_password }}"
        # Batch operation context
        batch_hostnames: "{{ var_hostnames }}"
        batch_ipaddresses: "{{ var_ipaddresses }}"
        batch_domain: "{{ var_domain }}"
        batch_action: "{{ var_action }}"
        batch_ttl: "{{ var_ttl }}"
        batch_manage_ptr: "{{ var_manage_ptr }}"
        # Runtime variables
        Environment: "{{ var_environment }}"
        dc_location: "{{ derived_dc_location }}"
        instance_group: "{{ selected_instance_group }}"
        # Metadata
        batch_processing: true
        added_timestamp: "{{ ansible_date_time.iso8601 }}"
      run_once: true

  handlers:
    - name: Update Jira Ticket
      ansible.builtin.include_tasks: manage-dns/handlers/update-sr-batch.yml

  tasks:
    - name: Include batch DNS management tasks
      ansible.builtin.include_tasks: manage-dns/tasks/main.yml

- name: Batch DNS Management Operations on ADMT Server
  hosts: "{{ hostvars['localhost']['selected_admt_server'] }}"
  gather_facts: false
  serial: 1

  tasks:
    - name: Execute batch DNS operations
      ansible.builtin.include_tasks: manage-dns/tasks/dns-batch-operations.yml
