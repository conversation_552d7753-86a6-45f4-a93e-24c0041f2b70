---
# =========================================================================
# Main tasks entry point for DNS management v2
# =========================================================================
# This file serves as the main entry point for DNS management tasks v2
# It includes the enhanced dns-operations.yml file which contains all the 
# DNS-related operations with AAP Instance Group routing support
# The 'dns' tag allows for selective execution of DNS tasks
#
# Enhanced Features v2:
# - Dynamic ADMT server selection with Instance Group routing
# - Enterprise automation with runtime variables
# - Modern Ansible practices with FQCN compatibility
#
# Author: CES Operational Excellence Team
# Contributor: Muhammad <PERSON><PERSON><PERSON> (7409)
# Version: 2.0
# =========================================================================

- name: Trigger Enhanced DNS Tasks with Instance Group Routing
  ansible.builtin.include_tasks: dns-operations.yml
  tags: dns
