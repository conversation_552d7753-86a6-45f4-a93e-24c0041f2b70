---
# =========================================================================
# DNS Management Variables v2 - Enhanced with AAP Instance Group Routing
# =========================================================================
# This file contains variables used throughout the DNS management playbook v2
# It includes paths, ticket information, email settings, credentials, and
# enhanced AAP Instance Group routing configuration
# All passwords are encrypted using Ansible Vault
#
# Enhanced Features v2:
# - ADMT server to Instance Group mapping for network segmentation
# - Runtime variable integration for enterprise automation
# - Dynamic server selection with intelligent routing
# - Modern Ansible practices with FQCN compatibility
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

# Script and file paths
script_path: C:\ansscripts\set-dns.ps1

# Ticket and job information
var_ticket: "{{ var_sr_number | default('SCR-XXXXX') }}"
aap_job_link: "https://aap.hcc.com.sg/#/jobs/playbook/{{ tower_job_id }}"

# PTR record management control
manage_ptr: true

# Email notification configuration
email_template: "{{ lookup('template', 'manage-dns/templates/email_template_dns.j2') }}"

# Team assignment based on domain
team_name: >-
          {{
            'Singhealth AD Team' if domain in ['ses.shsu.com.sg', 'shses.shs.com.sg'] else
            'Central AD Team'
          }}

# =========================================================================
# ENHANCED v2: ADMT Server to Domain Mapping
# =========================================================================
# Maps each domain to its corresponding ADMT server
admt_servers_map:
  devhealthgrp.com.sg: ["HISADMTVDSEC01.devhealthgrp.com.sg"]
  healthgrpexts.com.sg: ["HISADMTVSSEC01.healthgrpexts.com.sg"]
  nnstg.local: ["HISADMTVSSEC02.nnstg.local"]
  ses.shsu.com.sg: ["SHSADMTVDSEC02.ses.shsu.com.sg"]
  shses.shs.com.sg: ["SHSADMTVPSEC12.shses.shs.com.sg"]
  nhg.local: ["HISADMTVPSEC11.nhg.local"]
  aic.local: ["HISADMTVPSEC02.aic.local"]
  iltc.healthgrp.com.sg: ["HISADMTVPSEC04.iltc.healthgrp.com.sg"]
  healthgrp.com.sg: ["HISADMTVPSEC05.healthgrp.com.sg"]
  hcloud.healthgrp.com.sg: ["HISADMTVPSEC06.hcloud.healthgrp.com.sg"]
  healthgrpextp.com.sg: ["HISADMTVPSEC08.healthgrpextp.com.sg"]

# =========================================================================
# ENHANCED v2: ADMT Server to AAP Instance Group Mapping
# =========================================================================
# Maps each ADMT server to its required AAP Instance Group based on network segmentation
# HDC2 Instance Group: Special network segment with restricted access
# HDC1 Instance Group: Default network segment for most ADMT servers
admt_instance_group_mapping:
  # HDC2 Instance Group - Special network segment
  "HISADMTVSSEC01.healthgrpexts.com.sg": "SYP_H_HPC_MGT_UNXWIN_HDC2"
  "HISADMTVPSEC05.healthgrp.com.sg": "SYP_H_HPC_MGT_UNXWIN_HDC2"
  
  # HDC1 Instance Group - Default network segment
  "HISADMTVDSEC01.devhealthgrp.com.sg": "SYP_H_HPC_MGT_UNXWIN_HDC1"
  "HISADMTVSSEC02.nnstg.local": "SYP_H_HPC_MGT_UNXWIN_HDC1"
  "SHSADMTVDSEC02.ses.shsu.com.sg": "SYP_H_HPC_MGT_UNXWIN_HDC1"
  "SHSADMTVPSEC12.shses.shs.com.sg": "SYP_H_HPC_MGT_UNXWIN_HDC1"
  "HISADMTVPSEC11.nhg.local": "SYP_H_HPC_MGT_UNXWIN_HDC1"
  "HISADMTVPSEC02.aic.local": "SYP_H_HPC_MGT_UNXWIN_HDC1"
  "HISADMTVPSEC04.iltc.healthgrp.com.sg": "SYP_H_HPC_MGT_UNXWIN_HDC1"
  "HISADMTVPSEC06.hcloud.healthgrp.com.sg": "SYP_H_HPC_MGT_UNXWIN_HDC1"
  "HISADMTVPSEC08.healthgrpextp.com.sg": "SYP_H_HPC_MGT_UNXWIN_HDC1"

# Default Instance Group for any unmapped ADMT servers
default_instance_group: "SYP_H_HPC_MGT_UNXWIN_HDC1"

# =========================================================================
# ENHANCED v2: Runtime Variable Mapping for Enterprise Automation
# =========================================================================
# Standard runtime variables used across enterprise automation scenarios
runtime_variable_defaults:
  Environment: "production"
  dc_location_hdc1: "HDC1"
  dc_location_hdc2: "HDC2"
  default_ttl: "3600"
  default_manage_ptr: true

# =========================================================================
# Domain-Specific Service Account Credentials
# =========================================================================
# All passwords are encrypted using Ansible Vault for security

# Development Health Group Domain
var_dns_devhealthgrp_username: <EMAIL>
var_dns_devhealthgrp_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66663264653264653264653264653264653264653264653264653264653264653264653264653264
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532

# Health Group Extensions Domain
var_dns_healthgrpexts_username: <EMAIL>
var_dns_healthgrpexts_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  62623037653335363761396362393066626238613438653661323733366536386164336234366164
  6165363237366337363231633964626365633131623236390a613561626239336463653934343964
  66396364313762656364346530646532323333386637373238393064663364353839346433303738
  3830366463663461620a306138356466616130663639363438356237326362383162653566343431
  39356134326565613238366136306430386435666263646138306264653861623137

# NNSTG Local Domain
var_dns_nnstg_username: <EMAIL>
var_dns_nnstg_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  33323431636430366639666531663130313838316536623664646634356633636364653536656332
  3261636435613436613130323761626264376530313234370a326330383630376430356638386536
  34613832626536663837363131313133646165346436313562303162653935343863323735303261
  3631383931303064650a383462353536626432643763646539623464643932323837376535656462
  37653564663332383166306233363332343133666532613234323165356337323564

# SES SHSU Domain
var_dns_ses_username: <EMAIL>
var_dns_ses_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  32306632363763313964626433633930323038303462626631626364633938623336633334373032
  3638336334306561333239383464666465333336373963620a346138633463346266646132663064
  64316631373063316339353566653766366465323862663064326433373361616463323235356461
  6662346634356231320a353639613563613434613561313133326662386238343661356536623338
  33313465323231353531623937376234353166653663343264383033353365363532

# SHSES SHS Domain
var_dns_shses_username: <EMAIL>
var_dns_shses_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  34323538316462613765396634333663323637303165376634303331303066383661306537346665
  3031353866326233316264356138653335386135373764640a343766363530643132613532373761
  34343731613132356634323839623733623630636135623034626539663566616661643335643032
  3835623363363239340a383835636261343461616634383432306131623961363034666163343338
  34653662616630386166666635633236643330383765343838613633343464323066

# AIC Local Domain
var_dns_aic_username: <EMAIL>
var_dns_aic_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66663264653264653264653264653264653264653264653264653264653264653264653264653264
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532

# NHG Local Domain
var_dns_nhg_username: <EMAIL>
var_dns_nhg_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  30353361663035343038316638373663343632346530376464633465623762633237613037373935
  3033373434373438306539646139623031663364393135350a323931643862383836303161646263
  36333536316436653762326632663638613037393136613437633261653064303039653864353335
  3337386430663566320a663365333634383262346563663362643437396237333466626163663061
  61363532616666643532623438393965626633633762383866623837393031396265

# HCloud Health Group Domain
var_dns_hcloud_username: <EMAIL>
var_dns_hcloud_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  30616130343838333964663939363035633635393034346332343431653833653237633338623665
  3537656535373134666339396365386237353039636338360a386362326334656131663564646637
  62653036366164363534363935336663653136626436393566363734636134393131303465313831
  6132663662653032330a313864333136643630366238383236396166386234333231366130623461
  63313464613033303464353564386633623238306538303362363637666533363835

# Health Group Main Domain
var_dns_healthgrp_username: <EMAIL>
var_dns_healthgrp_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  35626566653732666430353135643763373538663437623231656565313631643835346430343833
  3137323834313431366635333163326536313232616162390a616339313132343063323031313564
  30323161346233313236386434303737656532643636306435623935663033656535353161376538
  3366646631313261390a636433616563616638313637333635353963356431396539373963343633
  34623534346537653439366637653662336538653231333562303430623939326562

# ILTC Health Group Domain
var_dns_iltc_username: <EMAIL>
var_dns_iltc_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66663264653264653264653264653264653264653264653264653264653264653264653264653264
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532

# Health Group External Portal Domain
var_dns_healthgrpextp_username: <EMAIL>
var_dns_healthgrpextp_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  66663264653264653264653264653264653264653264653264653264653264653264653264653264
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
  6532646532646532646532646532646532646532646532646532646532646532646532646532646532
