# Status Column Implementation for Jira Integration - v2

## Overview

Added a new "status" column to the Jira ticket update that shows whether the overall DNS job succeeded or failed. This provides immediate visibility into job outcomes directly in the Jira ticket, enhanced with v2-specific Instance Group information.

## Implementation Details

### 1. Status Tracking in Tasks

**File**: `manage-dns/tasks/dns-operations.yml`

```yaml
# After DNS script execution
- name: Set DNS operation status based on script execution for v2
  ansible.builtin.set_fact:
    dns_operation_status: "{{ 'SUCCESS' if dns_script_output.failed is not defined or not dns_script_output.failed else 'FAILED' }}"
  run_once: true
```

### 2. Status Determination in Handler

**File**: `manage-dns/handlers/update-sr-uat.yml`

```yaml
# Use the status set by the main tasks
- name: Determine overall job status for v2
  ansible.builtin.set_fact:
    overall_job_status: "{{ dns_operation_status | default('UNKNOWN') }}"
  run_once: true
```

### 3. Enhanced Jira API Call with v2 Information

**File**: `manage-dns/handlers/update-sr-uat.yml`

```yaml
# Updated Jira body with status column and v2 enhancements
body: |
  {
    "rows":[
      {
        "rowId":"{{ var_row_id }}",
        "columns":{
          "remark": "DNS v2 - {{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/ - ADMT: {{ hostvars[groups['admt_servers'][0]]['inventory_hostname'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }} - IG: {{ hostvars[groups['admt_servers'][0]]['instance_group'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }}",
          "status": "{{ overall_job_status }}"
        }
      }
    ]
  }
```

## Status Values

| Status | Description | When Set |
|--------|-------------|----------|
| `SUCCESS` | DNS operation completed successfully | When `dns_script_output.failed` is undefined or false |
| `FAILED` | DNS operation encountered errors | When `dns_script_output.failed` is true |
| `UNKNOWN` | Status could not be determined | When `dns_operation_status` is not set |

## v2 Specific Enhancements

### 1. Multi-Play Architecture Support
- Status tracking works across the dynamic host addition pattern
- Variables properly passed between localhost and ADMT server plays
- Handler execution coordinated between plays

### 2. Enhanced Jira Comments
The remark field now includes:
- DNS v2 identifier
- AAP job link
- ADMT server information
- Instance Group assignment
- Status in separate column

### 3. Instance Group Context
```yaml
# Example v2 Jira update
{
  "columns": {
    "remark": "DNS v2 - https://aap.hcc.com.sg/#/jobs/playbook/12345/ - ADMT: HISADMTVPSEC05.healthgrp.com.sg - IG: SYP_H_HPC_MGT_UNXWIN_HDC1",
    "status": "SUCCESS"
  }
}
```

## How It Works in v2 Architecture

### 1. Status Detection in Second Play
```yaml
# Executed on dynamically added ADMT server
- name: Set DNS operation status based on script execution for v2
  ansible.builtin.set_fact:
    dns_operation_status: "{{ 'SUCCESS' if dns_script_output.failed is not defined or not dns_script_output.failed else 'FAILED' }}"
  run_once: true
```

### 2. Handler Execution Context
- Handler defined in first play (localhost)
- Triggered from second play (ADMT server)
- Status variable accessible across plays
- Instance Group information available via hostvars

### 3. Variable Access Pattern
```yaml
# Accessing variables from different plays
overall_job_status: "{{ dns_operation_status | default('UNKNOWN') }}"
admt_server: "{{ hostvars[groups['admt_servers'][0]]['inventory_hostname'] }}"
instance_group: "{{ hostvars[groups['admt_servers'][0]]['instance_group'] }}"
```

## Example Jira Updates

### Successful v2 Job
```json
{
  "columns": {
    "remark": "DNS v2 - https://aap.hcc.com.sg/#/jobs/playbook/12345/ - ADMT: HISADMTVPSEC05.healthgrp.com.sg - IG: SYP_H_HPC_MGT_UNXWIN_HDC1",
    "status": "SUCCESS"
  }
}
```

### Failed v2 Job
```json
{
  "columns": {
    "remark": "DNS v2 - https://aap.hcc.com.sg/#/jobs/playbook/12345/ - ADMT: HISADMTVPSEC05.healthgrp.com.sg - IG: SYP_H_HPC_MGT_UNXWIN_HDC1",
    "status": "FAILED"
  }
}
```

## Benefits of v2 Implementation

1. **Enhanced Context**: Status + Instance Group + ADMT server info
2. **Network Visibility**: Shows which network segment (HDC1/HDC2) was used
3. **Troubleshooting**: ADMT server info helps with issue resolution
4. **Audit Trail**: Complete execution context in Jira
5. **Enterprise Integration**: Works with AAP Instance Group routing

## Testing Scenarios

### Test 1: Successful DNS Operation (v2)
```bash
# Variables:
domain: "healthgrp.com.sg"
hostname: "testserver"
var_action: "add"

# Expected Result:
# - ADMT server dynamically selected
# - DNS script executes successfully
# - dns_operation_status = "SUCCESS"
# - Jira status column = "SUCCESS"
# - Remark includes Instance Group info
```

### Test 2: Failed DNS Operation (v2)
```bash
# Variables:
domain: "invalid.domain"
hostname: "testserver"
var_action: "add"

# Expected Result:
# - DNS script fails
# - dns_operation_status = "FAILED"
# - Jira status column = "FAILED"
# - Full context preserved in remark
```

### Test 3: Manual AAP Execution (v2)
```bash
# Variables:
domain: "healthgrp.com.sg"
hostname: "testserver"
# No Jira variables

# Expected Result:
# - DNS operation completes
# - Status tracked internally
# - No Jira update attempted
# - Instance Group routing works
```

## Troubleshooting v2 Specific Issues

### Status Shows "UNKNOWN"
- Check if `dns_operation_status` is being set in dns-operations.yml
- Verify second play execution
- Check variable passing between plays

### Instance Group Info Missing
- Verify `add_host` task execution in first play
- Check `groups['admt_servers']` availability
- Confirm Instance Group assignment

### ADMT Server Info Incorrect
- Review dynamic host addition logic
- Check ADMT server selection mapping
- Verify hostvars access pattern

## Implementation Notes

- Status tracking works with v2's multi-play architecture
- Compatible with dynamic ADMT server selection
- Preserves all v2 enhancements (Instance Groups, network routing)
- Status determination independent of Jira update success
- Works for both Jira-triggered and manual executions
- Enhanced debugging with Instance Group context
