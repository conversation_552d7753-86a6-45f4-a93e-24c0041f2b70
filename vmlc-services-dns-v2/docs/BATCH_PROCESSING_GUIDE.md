# DNS Batch Processing Guide v2.0

## Overview

The DNS Management v2.0 system introduces enhanced batch processing capabilities that allow you to manage multiple hostnames for a single domain in one execution. This eliminates the need to run separate jobs for each hostname and provides consolidated reporting and Jira integration.

## Key Features

### 🚀 Batch Processing Capabilities
- **Multi-hostname support**: Process multiple hostnames in a single domain
- **Individual error isolation**: One hostname failure doesn't stop others
- **Consolidated reporting**: Single email with all hostname results
- **Unified Jira integration**: One Jira update for the entire batch
- **Backward compatibility**: Single hostname operations still work

### 🔧 Enhanced PowerShell Script
- **Batch operation functions**: New PowerShell functions for batch processing
- **Individual hostname tracking**: Each hostname has its own result status
- **Aggregated results**: Consolidated JSON output with batch summary
- **Error isolation**: PTR failures don't impact A record operations

### 📊 Improved Reporting
- **Batch email template**: Enhanced email with tabular hostname results
- **Individual status tracking**: Success/failure status for each hostname
- **Consolidated error reporting**: All errors in one place
- **Performance metrics**: Batch processing statistics

## Usage Examples

### Example 1: Batch Add Operation
```bash
ansible-playbook main-batch.yml \
  -e "hostname_list='server01,server02,server03'" \
  -e "domain='healthgrp.com.sg'" \
  -e "ip_address_list='*********,*********,*********'" \
  -e "var_action='add'" \
  -e "var_environment='prd'" \
  -e "var_sr_number='SCR-12345'" \
  -e "var_grid_id='12345'" \
  -e "var_row_id='67890'"
```

### Example 2: Batch Verify Operation
```bash
ansible-playbook main-batch.yml \
  -e "hostname_list='web01,web02,web03,web04'" \
  -e "domain='hcloud.healthgrp.com.sg'" \
  -e "var_action='verify'" \
  -e "var_environment='prd'"
```

### Example 3: Batch Remove Operation
```bash
ansible-playbook main-batch.yml \
  -e "hostname_list='old-server01,old-server02'" \
  -e "domain='healthgrpexts.com.sg'" \
  -e "var_action='remove'" \
  -e "var_environment='prd'" \
  -e "var_sr_number='SCR-54321'" \
  -e "var_grid_id='54321'" \
  -e "var_row_id='98765'"
```

## Required Variables

### Mandatory Variables
- **hostname_list**: Comma-separated list of hostnames (e.g., "server01,server02,server03")
- **domain**: Single domain for all hostnames (e.g., "healthgrp.com.sg")
- **var_action**: DNS operation ("verify", "add", "remove")

### Conditional Variables
- **ip_address_list**: Required for "add" operations (comma-separated, must match hostname count)
- **var_environment**: Required for Jira integration ("prd", "dev", "uat")

### Optional Variables
- **ttl**: DNS record TTL in seconds (default: 3600)
- **manage_ptr**: Enable/disable PTR management (default: true)
- **var_sr_number**: Jira Service Request number
- **var_grid_id**: Jira grid ID
- **var_row_id**: Jira row ID

## Input Validation

The system performs comprehensive validation:

1. **Hostname list validation**: Ensures hostname_list is provided and not empty
2. **Domain validation**: Ensures domain is provided and supported
3. **Action validation**: Ensures var_action is one of: verify, add, remove
4. **IP address validation**: For "add" operations, validates IP count matches hostname count
5. **Jira variables validation**: Checks required Jira variables when provided

## Batch Processing Flow

1. **Input Parsing**: Parse comma-separated hostname and IP address lists
2. **Validation**: Validate all inputs and requirements
3. **ADMT Server Selection**: Select appropriate ADMT servers for the domain
4. **Credential Selection**: Select domain-specific credentials
5. **Script Deployment**: Deploy enhanced PowerShell script to ADMT servers
6. **Batch Execution**: Execute batch DNS operations
7. **Result Processing**: Parse and aggregate individual hostname results
8. **Email Notification**: Send consolidated email with all results
9. **Jira Integration**: Update Jira with batch operation summary
10. **Cleanup**: Remove scripts and temporary files

## Output Format

### Batch JSON Output Structure
```json
{
  "batch_operation": true,
  "action": "add",
  "batch_status": "SUCCESS|PARTIAL_SUCCESS|FAILED",
  "domain": "healthgrp.com.sg",
  "dns_server": "hisaddcvputl07.healthgrp.com.sg",
  "ptr_management_enabled": true,
  "total_hostnames": 3,
  "successful_operations": 2,
  "failed_operations": 1,
  "hostname_results": [
    {
      "hostname_index": 1,
      "batch_hostname": "server01",
      "action": "add",
      "status": "A Record Added",
      "domain": "healthgrp.com.sg",
      "hostname": "server01",
      "ip_address": "*********",
      "individual_success": true,
      "ptr_operation_status": "Zone detected: 3-octet - PTR record created successfully"
    }
  ],
  "execution_summary": "Processed 3 hostnames: 2 successful, 1 failed"
}
```

## Error Handling

### Individual Hostname Isolation
- Each hostname is processed independently
- One hostname failure doesn't stop processing of others
- Failed hostnames are clearly identified in results

### Error Categories
1. **Input Validation Errors**: Invalid parameters or missing requirements
2. **DNS Server Errors**: DNS server connectivity or permission issues
3. **Individual Hostname Errors**: Specific hostname processing failures
4. **PTR Operation Errors**: PTR-specific errors (isolated from A record operations)

## Backward Compatibility

The v2.0 system maintains full backward compatibility:

- **Single hostname operations**: Use the original `main.yml` playbook
- **Existing variables**: All existing variable names and formats work
- **Legacy scripts**: Original PowerShell script functionality preserved
- **Email templates**: Original email template still available

## Performance Considerations

### Batch Size Recommendations
- **Small batches (1-5 hostnames)**: Optimal performance
- **Medium batches (6-15 hostnames)**: Good performance with detailed logging
- **Large batches (16+ hostnames)**: Consider splitting for better manageability

### Resource Usage
- **Memory**: Batch operations use more memory for result aggregation
- **Network**: Multiple DNS operations may increase network load
- **Time**: Batch operations take longer but are more efficient than multiple single operations

## Troubleshooting

### Common Issues
1. **Hostname count mismatch**: Ensure IP address count matches hostname count for "add" operations
2. **Domain not supported**: Verify domain is in the supported domains list
3. **Partial failures**: Check individual hostname results for specific error details
4. **Jira integration issues**: Verify all Jira variables are provided correctly

### Debug Information
- Enable debug output with `-vvv` flag
- Check individual hostname results in email report
- Review AAP job logs for detailed execution information
- Use batch operation summary for quick status overview

## Migration from v1.0

To migrate from single hostname operations to batch processing:

1. **Identify related hostnames**: Group hostnames by domain and operation
2. **Update job templates**: Create new job templates using `main-batch.yml`
3. **Modify variable inputs**: Change from single hostname to hostname list format
4. **Test with small batches**: Start with 2-3 hostnames to verify functionality
5. **Update documentation**: Update operational procedures for batch processing

## Support

For support with batch processing:
- **Primary Support**: CES Operational Excellence Team
- **Email**: <EMAIL>
- **Documentation**: DNS Management Project (vmlc-services-dns-v2)
- **Emergency Escalation**: Follow standard IT support procedures
