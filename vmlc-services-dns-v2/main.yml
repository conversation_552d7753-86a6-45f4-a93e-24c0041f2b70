---
# =========================================================================
# Main playbook for DNS Management v2 - Enhanced with AAP Instance Group Routing
# =========================================================================
# This playbook orchestrates the DNS management operations for VM lifecycle
# It handles A record and PTR record operations across multiple domains
# Operations supported: verify, add, remove
#
# Enhanced Features v2:
# - Dynamic ADMT server selection with AAP Instance Group routing
# - Enterprise automation with runtime variables (FQDN, PRD_IP, Environment, dc_location)
# - Intelligent network segmentation routing (HDC1/HDC2)
# - Modern Ansible practices with FQCN compatibility
# - Environment-based Jira integration (var_environment: "prd" | "dev" | "uat")
#
# Environment Variable:
#   var_environment: "prd" | "dev" | "uat" (determines Jira vars file)
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

- name: Dynamic ADMT Server Management and DNS Operations
  hosts: localhost
  gather_facts: false
  connection: local

  vars_files:
    - manage-dns/vars/dns_vars.yml

  vars:
    # Runtime variables for enterprise automation scenarios
    var_fqdn: "{{ FQDN | default('') }}"
    var_prd_ip: "{{ PRD_IP | default('') }}"
    var_environment: "{{ Environment | default('production') }}"
    var_dc_location: "{{ dc_location | default('') }}"

    # DNS operation variables
    var_action: "{{ var_action | default('verify') }}"
    var_domain: "{{ domain | default('') }}"
    var_hostname: "{{ hostname | default('') }}"
    var_ipaddress: "{{ ipaddress | default('') }}"
    var_ttl: "{{ ttl | default('3600') }}"
    var_manage_ptr: "{{ manage_ptr | default(true) }}"
    var_sr_number: "{{ var_sr_number | default('SCR-XXXXX') }}"

  pre_tasks:
    - name: Validate required runtime variables for DNS operations
      ansible.builtin.assert:
        that:
          - var_domain != ""
          - var_hostname != ""
          - var_action in ['verify', 'add', 'remove']
          - var_action != 'add' or var_ipaddress != ""
        fail_msg: |
          Required variables missing or invalid:
          - domain: {{ var_domain }}
          - hostname: {{ var_hostname }}
          - var_action: {{ var_action }}
          - ipaddress: {{ var_ipaddress }} (required for 'add' action)
        success_msg: "All required DNS operation variables validated successfully"
      run_once: true

    - name: Initialize logging for dynamic ADMT server management
      ansible.builtin.lineinfile:
        path: "/tmp/ansible-dns-v2.log"
        line: "{{ ansible_date_time.iso8601 }} - Starting DNS v2 operation: {{ var_action }} for {{ var_hostname }}.{{ var_domain }}"
        create: true
        mode: '0644'
      run_once: true

    - name: Determine target ADMT server based on domain
      ansible.builtin.set_fact:
        selected_admt_server: "{{ admt_servers_map[var_domain] | first }}"
        selected_instance_group: "{{ admt_instance_group_mapping[admt_servers_map[var_domain] | first] | default(default_instance_group) }}"
        derived_dc_location: "{{ 'HDC2' if admt_instance_group_mapping[admt_servers_map[var_domain] | first] | default(default_instance_group) == 'SYP_H_HPC_MGT_UNXWIN_HDC2' else 'HDC1' }}"
      when:
        - var_domain != ""
        - var_domain in admt_servers_map
      run_once: true

    - name: Validate ADMT server selection
      ansible.builtin.assert:
        that:
          - selected_admt_server is defined
          - selected_instance_group is defined
        fail_msg: "Failed to select ADMT server for domain: {{ var_domain }}. Check admt_servers_map configuration."
        success_msg: "ADMT server selected: {{ selected_admt_server }} → Instance Group: {{ selected_instance_group }}"
      run_once: true

    - name: Select credentials based on domain
      ansible.builtin.set_fact:
        selected_ansible_user: "{{ var_dns_devhealthgrp_username if var_domain == 'devhealthgrp.com.sg' else
          var_dns_healthgrpexts_username if var_domain == 'healthgrpexts.com.sg' else
          var_dns_nnstg_username if var_domain == 'nnstg.local' else
          var_dns_ses_username if var_domain == 'ses.shsu.com.sg' else
          var_dns_shses_username if var_domain == 'shses.shs.com.sg' else
          var_dns_nhg_username if var_domain == 'nhg.local' else
          var_dns_aic_username if var_domain == 'aic.local' else
          var_dns_iltc_username if var_domain == 'iltc.healthgrp.com.sg' else
          var_dns_healthgrp_username if var_domain == 'healthgrp.com.sg' else
          var_dns_hcloud_username if var_domain == 'hcloud.healthgrp.com.sg' else
          var_dns_healthgrpextp_username if var_domain == 'healthgrpextp.com.sg' else '' }}"
        selected_ansible_password: "{{ var_dns_devhealthgrp_password if var_domain == 'devhealthgrp.com.sg' else
          var_dns_healthgrpexts_password if var_domain == 'healthgrpexts.com.sg' else
          var_dns_nnstg_password if var_domain == 'nnstg.local' else
          var_dns_ses_password if var_domain == 'ses.shsu.com.sg' else
          var_dns_shses_password if var_domain == 'shses.shs.com.sg' else
          var_dns_nhg_password if var_domain == 'nhg.local' else
          var_dns_aic_password if var_domain == 'aic.local' else
          var_dns_iltc_password if var_domain == 'iltc.healthgrp.com.sg' else
          var_dns_healthgrp_password if var_domain == 'healthgrp.com.sg' else
          var_dns_hcloud_password if var_domain == 'hcloud.healthgrp.com.sg' else
          var_dns_healthgrpextp_password if var_domain == 'healthgrpextp.com.sg' else '' }}"
      no_log: true
      run_once: true

    - name: Validate credential selection
      ansible.builtin.assert:
        that:
          - selected_ansible_user != ""
          - selected_ansible_password != ""
        fail_msg: "Failed to select credentials for domain: {{ var_domain }}"
        success_msg: "Credentials selected for domain: {{ var_domain }}"
      run_once: true

    - name: Add ADMT server to inventory dynamically with Instance Group routing
      ansible.builtin.add_host:
        name: "{{ selected_admt_server }}"
        groups:
          - "admt_servers"
          - "{{ selected_instance_group }}"
        ansible_host: "{{ selected_admt_server }}"
        ansible_user: "{{ selected_ansible_user }}"
        ansible_password: "{{ selected_ansible_password }}"
        # Runtime variables for enterprise automation
        FQDN: "{{ selected_admt_server }}"
        PRD_IP: "{{ selected_admt_server }}"
        Environment: "{{ var_environment }}"
        dc_location: "{{ derived_dc_location }}"
        # DNS operation context
        target_domain: "{{ var_domain }}"
        dns_action: "{{ var_action }}"
        target_hostname: "{{ var_hostname }}"
        target_ip: "{{ var_ipaddress }}"
        target_ttl: "{{ var_ttl }}"
        target_manage_ptr: "{{ var_manage_ptr }}"
        # AAP Instance Group assignment
        instance_group: "{{ selected_instance_group }}"
        # Metadata
        dynamically_added: true
        added_timestamp: "{{ ansible_date_time.iso8601 }}"
      run_once: true

    - name: Log successful ADMT server addition
      ansible.builtin.lineinfile:
        path: "/tmp/ansible-dns-v2.log"
        line: "{{ ansible_date_time.iso8601 }} - Successfully added {{ selected_admt_server }} to Instance Group {{ selected_instance_group }}"
      run_once: true

    - name: Display Instance Group routing information
      ansible.builtin.debug:
        msg:
          - "=== AAP Instance Group Routing Information ==="
          - "Selected ADMT Server: {{ selected_admt_server }}"
          - "Assigned Instance Group: {{ selected_instance_group }}"
          - "Network Segment: {{ derived_dc_location }}"
          - "Target Domain: {{ var_domain }}"
          - "DNS Operation: {{ var_action }}"
          - "Target: {{ var_hostname }}.{{ var_domain }}"
          - "=============================================="
      run_once: true
      tags: ['instance_group_info']

    - name: Retrieve Password from PAM Safe
      ansible.builtin.include_role:
        name: cloud_cpe.cyberark_ccp.retrieve_from_cyberark
      when: false  # Disabled for now - enable when PAM integration is ready

  handlers:
    - name: Update Jira Ticket
      ansible.builtin.include_tasks: manage-dns/handlers/update-sr-uat.yml

  tasks:
    - name: Include enhanced DNS management tasks
      ansible.builtin.include_tasks: manage-dns/tasks/main.yml

- name: DNS Management Operations on Dynamically Added ADMT Server
  hosts: "{{ hostvars['localhost']['selected_admt_server'] }}"
  gather_facts: false
  serial: 1

  vars:
    # Inherit variables from the dynamically added host
    domain: "{{ target_domain }}"
    hostname: "{{ target_hostname }}"
    ipaddress: "{{ target_ip }}"
    ttl: "{{ target_ttl }}"
    manage_ptr: "{{ target_manage_ptr }}"
    var_action: "{{ dns_action }}"
    var_ticket: "{{ hostvars['localhost']['var_sr_number'] }}"

  tasks:
    - name: Validate inherited variables on ADMT server
      ansible.builtin.debug:
        msg:
          - "=== ADMT Server Variable Validation ==="
          - "ADMT Server: {{ inventory_hostname }}"
          - "Instance Group: {{ instance_group }}"
          - "Domain: {{ domain }}"
          - "Hostname: {{ hostname }}"
          - "IP Address: {{ ipaddress }}"
          - "Action: {{ var_action }}"
          - "DC Location: {{ dc_location }}"
          - "======================================"
      tags: ['validation']

    - name: Execute DNS operations on selected ADMT server
      ansible.builtin.include_tasks: manage-dns/tasks/dns-operations.yml
