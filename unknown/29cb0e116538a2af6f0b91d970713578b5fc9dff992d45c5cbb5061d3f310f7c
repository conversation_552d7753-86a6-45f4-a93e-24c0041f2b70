# Testing set-bpconf.py Script Locally

This document explains how to test the `set-bpconf.py` script directly on RedHat/Oracle Linux servers for NetBackup configuration validation.

## Overview

The `set-bpconf.py` script configures:
- NetBackup client configuration file (`/usr/openv/netbackup/bp.conf`)
- NetBackup server entries for all domains (2, 2B, 2C)
- Client name based on system hostname
- Connection options for NetBackup communication

## Prerequisites

### System Requirements
- RedHat Enterprise Linux 7/8/9 or Oracle Linux 7/8/9
- Python 3.6 or higher
- Root or sudo privileges
- `hostnamectl` command available

### Check Prerequisites
```bash
# Check Python version
python3 --version

# Check hostname command
which hostnamectl

# Check current hostname
hostnamectl --static

# Check sudo privileges
sudo -l

# Check if NetBackup directory exists
ls -la /usr/openv/ 2>/dev/null || echo "NetBackup directory not found (will be created)"
```

## Script Location and Permissions

```bash
# Copy the script to test location
cp /path/to/set-bpconf.py /tmp/set-bpconf.py

# Make executable
chmod +x /tmp/set-bpconf.py

# Verify permissions
ls -la /tmp/set-bpconf.py
```

## Usage Syntax

```bash
python3 /tmp/set-bpconf.py
```

**Note**: This script takes no parameters. It automatically detects the hostname and configures NetBackup accordingly.

## Testing Scenarios

### 1. Basic NetBackup Configuration Test

```bash
# Run the script
sudo python3 /tmp/set-bpconf.py
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] INFO: Starting NetBackup bp.conf configuration
[2024-01-15T10:30:45.234567] INFO: Configuring bp.conf for hostname: testserver
[2024-01-15T10:30:45.345678] INFO: Created directory: /usr/openv/netbackup
[2024-01-15T10:30:45.456789] INFO: Backed up existing bp.conf to /usr/openv/netbackup/bp.conf.backup.20240115_103045
[2024-01-15T10:30:45.567890] INFO: Successfully updated bp.conf with client name: testserver
[2024-01-15T10:30:45.678901] INFO: bp.conf verification successful: 34 servers configured
[2024-01-15T10:30:45.789012] INFO: NetBackup bp.conf configuration completed successfully
```

### 2. Test with Different Hostnames

```bash
# Set different hostname and test
sudo hostnamectl set-hostname testserver01
sudo python3 /tmp/set-bpconf.py

# Set FQDN hostname and test
sudo hostnamectl set-hostname testserver01.hcloud.healthgrp.com.sg
sudo python3 /tmp/set-bpconf.py
```

### 3. Test with Existing bp.conf

```bash
# Create a test bp.conf file first
sudo mkdir -p /usr/openv/netbackup
echo "# Test existing configuration" | sudo tee /usr/openv/netbackup/bp.conf

# Run script (should backup existing file)
sudo python3 /tmp/set-bpconf.py
```

## Verification Steps

### 1. Check NetBackup Directory Structure

```bash
# Verify directory was created
ls -la /usr/openv/netbackup/

# Check file permissions
ls -la /usr/openv/netbackup/bp.conf
```

### 2. Examine bp.conf Content

```bash
# View the complete bp.conf file
cat /usr/openv/netbackup/bp.conf

# Check for client name entry
grep "CLIENT_NAME" /usr/openv/netbackup/bp.conf

# Check for connection options
grep "CONNECT_OPTIONS" /usr/openv/netbackup/bp.conf

# Count server entries
grep "SERVER =" /usr/openv/netbackup/bp.conf | wc -l
```

### 3. Verify Server Entries

```bash
# Check Domain 2C servers
grep -A 10 "Domain 2C" /usr/openv/netbackup/bp.conf

# Check Domain 2B servers
grep -A 15 "Domain 2B" /usr/openv/netbackup/bp.conf

# Check Domain 2 servers
grep -A 15 "Domain 2" /usr/openv/netbackup/bp.conf

# Verify specific servers
grep "HISBKPVPMAS30" /usr/openv/netbackup/bp.conf
grep "HISBKPVPMAS20" /usr/openv/netbackup/bp.conf
grep "HISBKPVPMAS10" /usr/openv/netbackup/bp.conf
```

### 4. Check Backup Files

```bash
# List backup files
ls -la /usr/openv/netbackup/bp.conf.backup.*

# View backup content (if exists)
cat /usr/openv/netbackup/bp.conf.backup.* 2>/dev/null || echo "No backup files found"
```

## Advanced Testing

### 1. Test Idempotency

```bash
# Run the script multiple times
sudo python3 /tmp/set-bpconf.py
sudo python3 /tmp/set-bpconf.py
sudo python3 /tmp/set-bpconf.py

# Verify only one CLIENT_NAME entry exists
grep -c "CLIENT_NAME" /usr/openv/netbackup/bp.conf
```

### 2. Test with Different Hostname Formats

```bash
# Test with short hostname
sudo hostnamectl set-hostname server01
sudo python3 /tmp/set-bpconf.py
grep "CLIENT_NAME = server01" /usr/openv/netbackup/bp.conf

# Test with FQDN
sudo hostnamectl set-hostname server01.hcloud.healthgrp.com.sg
sudo python3 /tmp/set-bpconf.py
grep "CLIENT_NAME = server01" /usr/openv/netbackup/bp.conf  # Should still use short name
```

### 3. Test with Existing Configuration

```bash
# Create bp.conf with existing content
sudo tee /usr/openv/netbackup/bp.conf << EOF
# Existing configuration
SOME_OPTION = value1
ANOTHER_OPTION = value2
EOF

# Run script
sudo python3 /tmp/set-bpconf.py

# Verify existing content is preserved (if not SERVER/CLIENT_NAME related)
cat /usr/openv/netbackup/bp.conf
```

## Error Condition Testing

### 1. Test Without Sudo

```bash
# Should fail with permission error
python3 /tmp/set-bpconf.py
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Failed to create directory /usr/openv/netbackup: Permission denied
```

### 2. Test with Invalid Hostname

```bash
# Set invalid hostname (if possible)
sudo hostnamectl set-hostname ""
sudo python3 /tmp/set-bpconf.py
```

### 3. Test with Read-Only Filesystem

```bash
# Make filesystem read-only (simulate)
sudo mount -o remount,ro /usr 2>/dev/null || echo "Cannot remount /usr as read-only"

# Try to run script
sudo python3 /tmp/set-bpconf.py

# Restore write access
sudo mount -o remount,rw /usr 2>/dev/null || echo "Filesystem not remounted"
```

## Performance Testing

```bash
# Time the execution
time sudo python3 /tmp/set-bpconf.py

# Test multiple runs
time (
    for i in {1..5}; do
        sudo python3 /tmp/set-bpconf.py
    done
)
```

## Configuration Validation

### 1. Verify All Required Servers

```bash
# Expected server count: 34 servers total
expected_servers=(
    "HISBKPVPMAS30" "HISBKPPPMAS16" "HISBKPPPMAS66"  # Domain 2C
    "HISBKPVPMAS20" "HISBKPPPMAS15" "HISBKPPPMAS65"  # Domain 2B  
    "HISBKPVPMAS10" "HISBKPPPMAS12" "HISBKPVPMAS13"  # Domain 2
)

# Check each server exists
for server in "${expected_servers[@]}"; do
    if grep -q "SERVER = $server" /usr/openv/netbackup/bp.conf; then
        echo "✓ Found: $server"
    else
        echo "✗ Missing: $server"
    fi
done
```

### 2. Validate Configuration Format

```bash
# Check CLIENT_NAME format
client_name=$(grep "CLIENT_NAME" /usr/openv/netbackup/bp.conf | cut -d'=' -f2 | tr -d ' ')
echo "Client name: $client_name"

# Verify CONNECT_OPTIONS
connect_opts=$(grep "CONNECT_OPTIONS" /usr/openv/netbackup/bp.conf)
echo "Connect options: $connect_opts"

# Check for duplicate entries
echo "Duplicate CLIENT_NAME entries: $(grep -c "CLIENT_NAME" /usr/openv/netbackup/bp.conf)"
echo "Duplicate CONNECT_OPTIONS entries: $(grep -c "CONNECT_OPTIONS" /usr/openv/netbackup/bp.conf)"
```

### 3. Test NetBackup Compatibility

```bash
# If NetBackup client is installed, test configuration
if command -v bplist &> /dev/null; then
    echo "Testing NetBackup client connectivity..."
    sudo bplist -C $(hostname -s) 2>&1 | head -10
else
    echo "NetBackup client not installed - configuration syntax only"
fi
```

## Integration Testing

### Test with Ansible Variables

```bash
# Simulate Ansible execution environment
export HOSTNAME=$(hostname -s)
sudo python3 /tmp/set-bpconf.py

# Verify hostname was used correctly
grep "CLIENT_NAME = $HOSTNAME" /usr/openv/netbackup/bp.conf
```

### Test Configuration Parsing

```bash
# Parse and validate the configuration
sudo python3 -c "
with open('/usr/openv/netbackup/bp.conf', 'r') as f:
    content = f.read()
    
servers = [line for line in content.split('\n') if line.startswith('SERVER =')]
client_lines = [line for line in content.split('\n') if line.startswith('CLIENT_NAME =')]
connect_lines = [line for line in content.split('\n') if line.startswith('CONNECT_OPTIONS =')]

print(f'Servers configured: {len(servers)}')
print(f'Client name entries: {len(client_lines)}')
print(f'Connect option entries: {len(connect_lines)}')

if len(client_lines) == 1 and len(connect_lines) == 1:
    print('✓ Configuration format is correct')
else:
    print('✗ Configuration format has issues')
"
```

## Cleanup After Testing

```bash
# Remove test configuration
sudo rm -f /usr/openv/netbackup/bp.conf

# Remove backup files
sudo rm -f /usr/openv/netbackup/bp.conf.backup.*

# Remove test directory (if empty)
sudo rmdir /usr/openv/netbackup 2>/dev/null || echo "Directory not empty or doesn't exist"
sudo rmdir /usr/openv 2>/dev/null || echo "Directory not empty or doesn't exist"

# Remove test script
rm /tmp/set-bpconf.py

# Restore original hostname if changed
sudo hostnamectl set-hostname original-hostname
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Ensure running with sudo
   sudo python3 /tmp/set-bpconf.py
   
   # Check directory permissions
   ls -la /usr/openv/
   ```

2. **Directory Creation Failed**
   ```bash
   # Check filesystem space
   df -h /usr
   
   # Check parent directory permissions
   ls -la /usr/
   ```

3. **Hostname Detection Issues**
   ```bash
   # Check hostname command
   hostnamectl --static
   hostname -s
   
   # Verify hostname format
   hostname | grep -E '^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]$'
   ```

4. **File Write Issues**
   ```bash
   # Check filesystem mount options
   mount | grep /usr
   
   # Check available inodes
   df -i /usr
   ```

## Best Practices for Local Testing

1. **Backup First**: Always backup existing bp.conf if it exists
2. **Test Incrementally**: Start with basic functionality, then test edge cases
3. **Verify Results**: Always check the generated configuration
4. **Test Rollback**: Ensure you can restore original configuration
5. **Document Findings**: Keep notes of test results and any issues
6. **Use Test Hostnames**: Don't test with production hostnames

## Expected Configuration Structure

After successful execution, `/usr/openv/netbackup/bp.conf` should contain:

```
SERVER = HISBKPVPMAS30
SERVER = HISBKPPPMAS16
[... 32 more SERVER entries ...]
CLIENT_NAME = hostname
CONNECT_OPTIONS = localhost 1 0 2
```

## Security Considerations

- Script requires root privileges to create directories and files
- Validates hostname format to prevent injection attacks
- Creates backups before making changes
- Uses safe file operations with temporary files
- Proper file permissions (644) are set on configuration files
