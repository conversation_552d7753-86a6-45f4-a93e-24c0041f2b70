# DNS Management Project Usage Guide

## 1. Introduction

This document provides detailed instructions on how to use the DNS Management project (vmlc-services-dns-v1) through Ansible Automation Platform (AAP) Job Templates.

The project features intelligent PTR record management with hierarchical zone detection and safe automatic PTR removal capabilities.

**Author:** CES Operational Excellence Team
**Contributor:** Muhammad <PERSON> (7409)

## 1.1 Key Features

- **Intelligent PTR Zone Detection**: Automatically detects optimal PTR zones using 3-tier hierarchical fallback (3→2→1 octet zones)
- **Safe PTR Management**: Only removes PTR records that match the hostname being removed
- **Comprehensive Error Handling**: PTR operations never impact A record success
- **Enhanced Logging**: Detailed operation logs with timing information and color-coded output
- **Timeout Protection**: Prevents script hanging with DNS query timeouts
- **Backward Compatibility**: Existing functionality preserved with new optional features

## 2. AAP Job Templates

The following Job Templates should be created in AAP for DNS management operations:

### 2.1 DNS Record Verification

- **Name**: VMLC - DNS Record Verification
- **Description**: Verify if a DNS A record and its corresponding PTR record exist with intelligent zone detection
- **Job Type**: Run
- **Inventory**: Your inventory
- **Project**: vmlc-services-dns-v1
- **Playbook**: main.yml
- **Credentials**: Machine credential with appropriate permissions
- **Extra Variables**:
  ```yaml
  var_action: verify
  domain: <domain>
  hostname: <hostname>
  manage_ptr: true  # Optional, defaults to true
  ```

### 2.2 DNS Record Addition

- **Name**: VMLC - DNS Record Addition
- **Description**: Add a DNS A record and its corresponding PTR record with intelligent zone management
- **Job Type**: Run
- **Inventory**: Your inventory
- **Project**: vmlc-services-dns-v1
- **Playbook**: main.yml
- **Credentials**: Machine credential with appropriate permissions
- **Extra Variables**:
  ```yaml
  var_action: add
  domain: <domain>
  hostname: <hostname>
  ipaddress: <ip_address>
  ttl: <ttl_value>  # Optional, defaults to 3600
  manage_ptr: true  # Optional, defaults to true
  ```

### 2.3 DNS Record Removal

- **Name**: VMLC - DNS Record Removal
- **Description**: Remove a DNS A record and its matching PTR record safely
- **Job Type**: Run
- **Inventory**: Your inventory
- **Project**: vmlc-services-dns-v1
- **Playbook**: main.yml
- **Credentials**: Machine credential with appropriate permissions
- **Extra Variables**:
  ```yaml
  var_action: remove
  domain: <domain>
  hostname: <hostname>
  manage_ptr: true  # Optional, defaults to true
  ```

## 3. Using Extra Variables

### 3.1 Required Variables

- **var_action**: The operation to perform (verify, add, remove)
- **domain**: The domain for the DNS record (e.g., healthgrp.com.sg)
- **hostname**: The hostname for the DNS record (without the domain)

### 3.2 Optional Variables

- **ipaddress**: The IP address for the DNS record (required for 'add' var_action)
- **ttl**: The time-to-live value in seconds (defaults to 3600)
- **manage_ptr**: Enable/disable PTR record management (defaults to true)
- **var_sr_number**: The Jira Service Request number (defaults to SCR-67898)
- **testing_mode**: Set to 'true' to send emails <NAME_EMAIL> for testing

### 3.3 PTR Management Options

The `manage_ptr` variable controls intelligent PTR record operations:

- **true** (default): Enables intelligent PTR zone detection and management
  - For **add**: Creates PTR records in the optimal detected zone
  - For **verify**: Checks PTR records using intelligent zone detection
  - For **remove**: Safely removes matching PTR records only
- **false**: Disables PTR operations, works with A records only
  - Maintains backward compatibility with legacy behavior
  - Useful when PTR zones are not available or managed separately

### 3.4 Examples

#### Verify a DNS Record with PTR Management

```yaml
var_action: verify
domain: healthgrp.com.sg
hostname: server01
manage_ptr: true  # Uses intelligent zone detection
```

#### Add a DNS Record with Intelligent PTR Management

```yaml
var_action: add
domain: healthgrp.com.sg
hostname: server01
ipaddress: ********
ttl: 7200
manage_ptr: true  # Creates PTR in optimal detected zone
```

#### Remove a DNS Record with Safe PTR Removal

```yaml
var_action: remove
domain: healthgrp.com.sg
hostname: server01
manage_ptr: true  # Removes matching PTR records only
```

#### A Record Only Operations (Legacy Mode)

```yaml
var_action: add
domain: healthgrp.com.sg
hostname: server01
ipaddress: ********
manage_ptr: false  # Disables PTR operations
```

#### Testing Mode with PTR Management

```yaml
var_action: add
domain: healthgrp.com.sg
hostname: server01
ipaddress: ********
manage_ptr: true
testing_mode: true
```

## 4. AAP Survey Configuration

Instead of using extra variables, you can also configure AAP Job Template Surveys for a more user-friendly interface.

### 4.1 DNS Record Verification Survey

| Question | Variable Name | Type | Default | Required |
|----------|---------------|------|---------|----------|
| Operation | var_action | Multiple Choice (verify, add, remove) | verify | Yes |
| Domain | domain | Multiple Choice (list of supported domains) | - | Yes |
| Hostname | hostname | Text | - | Yes |
| PTR Management | manage_ptr | Multiple Choice (true, false) | true | No |
| Jira SR Number | var_sr_number | Text | SCR-67898 | No |

### 4.2 DNS Record Addition Survey

| Question | Variable Name | Type | Default | Required |
|----------|---------------|------|---------|----------|
| Operation | var_action | Multiple Choice (verify, add, remove) | add | Yes |
| Domain | domain | Multiple Choice (list of supported domains) | - | Yes |
| Hostname | hostname | Text | - | Yes |
| IP Address | ipaddress | Text | - | Yes |
| TTL (seconds) | ttl | Integer | 3600 | No |
| PTR Management | manage_ptr | Multiple Choice (true, false) | true | No |
| Jira SR Number | var_sr_number | Text | SCR-67898 | No |

### 4.3 DNS Record Removal Survey

| Question | Variable Name | Type | Default | Required |
|----------|---------------|------|---------|----------|
| Operation | var_action | Multiple Choice (verify, add, remove) | remove | Yes |
| Domain | domain | Multiple Choice (list of supported domains) | - | Yes |
| Hostname | hostname | Text | - | Yes |
| PTR Management | manage_ptr | Multiple Choice (true, false) | true | No |
| Jira SR Number | var_sr_number | Text | SCR-67898 | No |

## 5. Email Notifications

After each operation, an email notification is sent to the relevant team with enhanced PTR management information:

- For SingHealth domains (ses.shsu.com.sg, shses.shs.com.sg): <EMAIL>
- For all other domains: <EMAIL>

A BCC is always <NAME_EMAIL>.

### 5.1 Enhanced Email Content

The email notifications now include detailed PTR management information:

- **PTR Management Status**: Whether PTR operations were enabled or disabled
- **PTR Zone Detection Results**: Which reverse zone was detected and used
- **PTR Operation Status**: Detailed status of PTR operations (success, failure, skipped)
- **PTR Record Information**: Current state of PTR records
- **Timing Information**: Zone detection timing for performance monitoring

## 6. Jira Integration

If a Jira SR number is provided, the job will update the Jira ticket with a link to the AAP job.

## 7. Idempotent Operations

All DNS operations in this project are designed to be idempotent, which means they can be run multiple times without causing unintended side effects. This is particularly important for operations that might be interrupted or need to be retried.

### 7.1 How Idempotency Works

- **Verify Operation**: Always safe to run multiple times as it only reads data
- **Add Operation**:
  - Checks if the A record exists before adding
  - If it exists with the same IP, no action is taken
  - If it exists with a different IP, it's updated
  - PTR records are only added if they don't exist in the detected zone
  - Intelligent zone detection ensures PTR records are created in the optimal zone
- **Remove Operation**:
  - Checks if the A record exists before removing
  - If it doesn't exist, completes successfully without error
  - PTR records are only removed if they match the hostname exactly
  - No unexpected errors if run multiple times

### 7.2 Enhanced Idempotency with PTR Management

- **Zone Detection**: Intelligent zone detection is performed each time, ensuring optimal zone selection
- **Safe PTR Removal**: Only removes PTR records that match the hostname being removed
- **Graceful Fallback**: Operations continue successfully even if PTR zones are unavailable
- **Error Isolation**: PTR operation failures never impact A record operation success

### 7.3 Benefits for Operations

- **Safe Retries**: If an operation fails due to network issues, it can be safely retried
- **Consistent State**: The DNS records will be in the expected state regardless of how many times the operation is run
- **Reduced Risk**: No risk of creating duplicate records or causing DNS conflicts
- **Clear Reporting**: The email notification clearly indicates what changes were made (if any)
- **PTR Safety**: PTR records are never accidentally removed or created in wrong zones

### 7.4 Example Scenarios

1. **Adding the Same Record Twice**: If you run the add operation for the same hostname and IP address twice, the second operation will detect that both A and PTR records already exist and will not make any changes.

2. **Removing a Non-existent Record**: If you run the remove operation for a record that doesn't exist, the operation will complete successfully without error.

3. **Interrupted Operation**: If an operation is interrupted (e.g., network failure), it can be safely retried without risk of creating an inconsistent state.

4. **PTR Zone Changes**: If PTR zones are restructured, the intelligent zone detection will automatically adapt to use the new optimal zone.

5. **Mixed PTR States**: If some PTR records exist in different zones, the system will detect and use the appropriate zone for each operation.

## 8. Intelligent PTR Zone Detection

### 8.1 How Zone Detection Works

The system uses a 3-tier hierarchical fallback approach to find the optimal PTR zone:

1. **3-octet zone** (most specific): `30.20.10.in-addr.arpa` for IP `***********`
2. **2-octet zone** (medium): `20.10.in-addr.arpa` for IP `***********`
3. **1-octet zone** (least specific): `10.in-addr.arpa` for IP `***********`

### 8.2 Zone Detection Process

1. **Timeout Protection**: Each zone check has a 10-second timeout to prevent hanging
2. **Sequential Testing**: Zones are tested from most specific to least specific
3. **First Match Wins**: The first available zone is selected for PTR operations
4. **Graceful Fallback**: If no zones are found, A record operations continue normally

### 8.3 PTR Record Naming

The PTR record name is calculated based on the detected zone:

- **3-octet zone**: Record name = last octet (e.g., "40")
- **2-octet zone**: Record name = last two octets (e.g., "40.30")
- **1-octet zone**: Record name = last three octets (e.g., "40.30.20")

## 9. Safety Features

### 9.1 PTR Removal Safety

- **Hostname Verification**: Only removes PTR records that point to the exact hostname being removed
- **Zone Preservation**: Never deletes DNS zones, only individual PTR records
- **Error Isolation**: PTR operation failures don't affect A record operations
- **Detailed Logging**: All PTR operations are logged with specific reasons

### 9.2 Operational Safety

- **A Record Priority**: A record operations always complete regardless of PTR status
- **Backward Compatibility**: Legacy behavior preserved when `manage_ptr = false`
- **Comprehensive Error Handling**: Detailed error messages without operation termination
- **Idempotent Operations**: Safe to run multiple times without side effects

## 10. Limitations

- The project can only be triggered from AAP
- Users and developers do not have access to run ansible-playbook commands directly on servers
- PTR zone detection requires network connectivity to DNS servers
- Special handling is required for SingHealth domains
- PTR operations depend on the availability of reverse DNS zones
