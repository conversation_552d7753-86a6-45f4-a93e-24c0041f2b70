#!/usr/bin/env python3
"""
Jira Grid API Test Script
========================

This script helps test the Jira Grid API connection and update functionality
to troubleshoot issues with the Ansible handler.

Usage:
    python3 test_jira_api.py

Requirements:
    pip install requests

Author: CES Operational Excellence Team
"""

import requests
import json
import sys
from urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings for testing
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# Configuration - Update these values
CONFIG = {
    "grid_url": "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid",
    "grid_id": "YOUR_GRID_ID",           # Replace with actual grid ID
    "sr_number": "SCR-XXXXX",            # Replace with actual SR number
    "row_id": "YOUR_ROW_ID",             # Replace with actual row ID
    "authorization": "Bearer YOUR_TOKEN", # Replace with actual token
    "aap_url": "https://aap.hcc.com.sg",
    "tower_job_id": "12345"              # Test job ID
}

def test_connectivity():
    """Test basic connectivity to Jira Grid API"""
    print("=== Testing Jira Grid API Connectivity ===")
    
    url = f"{CONFIG['grid_url']}/{CONFIG['grid_id']}/issue/{CONFIG['sr_number']}/"
    headers = {
        "Authorization": CONFIG["authorization"],
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers, verify=False, timeout=30)
        print(f"GET Request URL: {url}")
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ Connectivity: SUCCESS")
            try:
                data = response.json()
                print(f"Response Data: {json.dumps(data, indent=2)}")
            except:
                print(f"Response Text: {response.text}")
        else:
            print("❌ Connectivity: FAILED")
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Connection Error: {str(e)}")
    
    print()

def test_update():
    """Test updating the Jira grid"""
    print("=== Testing Jira Grid Update ===")
    
    url = f"{CONFIG['grid_url']}/{CONFIG['grid_id']}/issue/{CONFIG['sr_number']}/"
    headers = {
        "Authorization": CONFIG["authorization"],
        "Content-Type": "application/json"
    }
    
    # Test payload
    payload = {
        "rows": [
            {
                "rowId": CONFIG["row_id"],
                "columns": {
                    "remark": f"{CONFIG['aap_url']}/#/jobs/playbook/{CONFIG['tower_job_id']}/",
                    "status": "SUCCESS"
                }
            }
        ]
    }
    
    print(f"PUT Request URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.put(
            url, 
            headers=headers, 
            json=payload, 
            verify=False, 
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code in [200, 204]:
            print("✅ Update: SUCCESS")
            print("Check your Jira grid to see if the update appeared")
        else:
            print("❌ Update: FAILED")
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Update Error: {str(e)}")
    
    print()

def test_minimal_update():
    """Test with minimal payload (only remark column)"""
    print("=== Testing Minimal Update (Remark Only) ===")
    
    url = f"{CONFIG['grid_url']}/{CONFIG['grid_id']}/issue/{CONFIG['sr_number']}/"
    headers = {
        "Authorization": CONFIG["authorization"],
        "Content-Type": "application/json"
    }
    
    # Minimal payload
    payload = {
        "rows": [
            {
                "rowId": CONFIG["row_id"],
                "columns": {
                    "remark": f"Test Update - {CONFIG['tower_job_id']}"
                }
            }
        ]
    }
    
    print(f"PUT Request URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.put(
            url, 
            headers=headers, 
            json=payload, 
            verify=False, 
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code in [200, 204]:
            print("✅ Minimal Update: SUCCESS")
        else:
            print("❌ Minimal Update: FAILED")
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Minimal Update Error: {str(e)}")
    
    print()

def validate_config():
    """Validate configuration values"""
    print("=== Configuration Validation ===")
    
    issues = []
    
    if CONFIG["grid_id"] == "YOUR_GRID_ID":
        issues.append("❌ grid_id not set")
    else:
        print(f"✅ Grid ID: {CONFIG['grid_id']}")
    
    if CONFIG["sr_number"] == "SCR-XXXXX":
        issues.append("❌ sr_number not set")
    else:
        print(f"✅ SR Number: {CONFIG['sr_number']}")
    
    if CONFIG["row_id"] == "YOUR_ROW_ID":
        issues.append("❌ row_id not set")
    else:
        print(f"✅ Row ID: {CONFIG['row_id']}")
    
    if CONFIG["authorization"] == "Bearer YOUR_TOKEN":
        issues.append("❌ authorization token not set")
    else:
        print("✅ Authorization token set")
    
    if issues:
        print("\n".join(issues))
        print("\n⚠️  Please update the CONFIG section in this script with your actual values")
        return False
    
    print("✅ All configuration values set")
    print()
    return True

def main():
    """Main test function"""
    print("Jira Grid API Test Script")
    print("=" * 50)
    
    if not validate_config():
        sys.exit(1)
    
    test_connectivity()
    test_minimal_update()
    test_update()
    
    print("=== Test Complete ===")
    print("If all tests pass but Ansible still fails, check:")
    print("1. Variable values in Ansible match this script")
    print("2. Jira grid column names ('remark', 'status')")
    print("3. Row ID exists and is correct")
    print("4. Grid permissions for the service account")

if __name__ == "__main__":
    main()
