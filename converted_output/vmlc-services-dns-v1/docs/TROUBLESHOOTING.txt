# DNS Management Project Troubleshooting Guide

## 1. Introduction

This document provides troubleshooting guidance for common issues that may be encountered when using the DNS Management project (vmlc-services-dns-v1).

**Author:** CES Operational Excellence Team  
**Contributor:** <PERSON> (7409)

## 2. Common Issues and Solutions

### 2.1 DNS Server Connection Issues

**Issue**: Unable to connect to DNS server.

**Possible Causes**:
- Network connectivity issues
- Incorrect DNS server name
- DNS server is down

**Solutions**:
1. Verify network connectivity to the DNS server
2. Check if the DNS server name is correct in the PowerShell script
3. Verify that the DNS server is running
4. Check if the service account has appropriate permissions

### 2.2 ADMT Server Connection Issues

**Issue**: Unable to connect to ADMT server.

**Possible Causes**:
- Network connectivity issues
- Incorrect ADMT server name
- ADMT server is down

**Solutions**:
1. Verify network connectivity to the ADMT server
2. Check if the ADMT server name is correct in the dns.yml file
3. Verify that the ADMT server is running
4. Check if the service account has appropriate permissions

### 2.3 Authentication Issues

**Issue**: Authentication failure when connecting to ADMT or DNS servers.

**Possible Causes**:
- Incorrect service account credentials
- Service account is locked or expired
- Service account lacks necessary permissions

**Solutions**:
1. Verify that the service account credentials are correct
2. Check if the service account is locked or expired
3. Ensure the service account has the necessary permissions
4. Verify that the CyberArk integration is working correctly

### 2.4 DNS Record Operation Failures

**Issue**: DNS record operations (verify, add, remove) fail.

**Possible Causes**:
- Service account lacks necessary permissions
- DNS zone does not exist
- DNS server is not authoritative for the zone
- Invalid hostname or IP address format

**Solutions**:
1. Check the error message in the email notification
2. Verify that the service account has the necessary permissions
3. Ensure the DNS zone exists on the DNS server
4. Verify that the DNS server is authoritative for the zone
5. Check the hostname and IP address format

### 2.5 PTR Record Operation Failures

**Issue**: PTR record operations fail.

**Possible Causes**:
- Service account lacks necessary permissions
- Reverse lookup zone does not exist
- DNS server is not authoritative for the reverse lookup zone
- Invalid IP address format

**Solutions**:
1. Check the error message in the email notification
2. Verify that the service account has the necessary permissions
3. Ensure the reverse lookup zone exists on the DNS server
4. Verify that the DNS server is authoritative for the reverse lookup zone
5. Check the IP address format

### 2.6 Email Notification Issues

**Issue**: Email notifications are not being sent.

**Possible Causes**:
- SMTP server is down
- Incorrect SMTP server configuration
- Email addresses are incorrect

**Solutions**:
1. Verify that the SMTP server is running
2. Check the SMTP server configuration in the dns.yml file
3. Ensure the email addresses are correct
4. Check if the testing_mode variable is set to true

### 2.7 Jira Integration Issues

**Issue**: Jira tickets are not being updated.

**Possible Causes**:
- Jira server is down
- Incorrect Jira server configuration
- Jira credentials are incorrect
- Jira ticket does not exist

**Solutions**:
1. Verify that the Jira server is running
2. Check the Jira server configuration in the uat_vars.yml or prod_vars.yml file
3. Ensure the Jira credentials are correct
4. Verify that the Jira ticket exists

## 3. Debugging

### 3.1 Enabling Verbose Output

To enable verbose output for debugging, add the `-v` flag to the ansible-playbook command or enable verbose output in the AAP Job Template.

### 3.2 Checking Logs

- AAP job logs: Available in the AAP web interface
- PowerShell script logs: Included in the email notification
- DNS server logs: Available on the DNS servers

### 3.3 Testing in Isolation

To test the PowerShell script in isolation:

1. Connect to the ADMT server
2. Copy the set-dns.ps1 script to the server
3. Run the script with the appropriate parameters
4. Check the output

Example:
```powershell
.\set-dns.ps1 -Action verify -Domain healthgrp.com.sg -HostName server01
```

## 4. Contact Information

For additional assistance, please contact:

- **Email**: <EMAIL>
- **Jira**: Create a Service Request ticket with the CES Operational Excellence Team

## 5. Known Issues

### 5.1 PTR Record Removal

PTR records are not automatically removed when removing A records. This is by design for safety reasons. If you need to remove a PTR record, you must do so manually on the DNS server.

### 5.2 SingHealth Domain Special Handling

SingHealth domains (ses.shsu.com.sg and shses.shs.com.sg) require special handling for PTR records. The PTR records are managed on different servers than the A records. This is by design and cannot be changed.
