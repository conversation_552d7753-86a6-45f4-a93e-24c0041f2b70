# Testing set-disks.py Script Locally

This document explains how to test the `set-disks.py` script directly on RedHat/Oracle Linux servers for disk management and filesystem configuration.

## Overview

The `set-disks.py` script manages:
- Disk partitioning and LVM configuration
- XFS filesystem creation and mounting
- Automatic `/etc/fstab` configuration
- Mount point creation and validation
- Multiple filesystem configurations via JSON payload

## Prerequisites

### System Requirements
- RedHat Enterprise Linux 7/8/9 or Oracle Linux 7/8/9
- Python 3.6 or higher
- Root or sudo privileges
- LVM tools (lvm2), XFS utilities (xfsprogs), and partitioning tools (parted)
- Available unmounted disks for testing

### Check Prerequisites
```bash
# Check Python version
python3 --version

# Check required tools
which lsblk pvs vgs lvs mkfs.xfs parted

# Check available disks
lsblk -d -o NAME,TYPE,SIZE,MOUNTPOINT

# Check current LVM configuration
sudo pvs
sudo vgs
sudo lvs

# Check sudo privileges
sudo -l
```

### Install Required Packages
```bash
# RHEL/Oracle Linux
sudo yum install lvm2 xfsprogs parted -y
# or
sudo dnf install lvm2 xfsprogs parted -y
```

## Script Location and Permissions

```bash
# Copy the script to test location
cp /path/to/set-disks.py /tmp/set-disks.py

# Make executable
chmod +x /tmp/set-disks.py

# Verify permissions
ls -la /tmp/set-disks.py
```

## Usage Syntax

```bash
# List available disks
python3 /tmp/set-disks.py --list-disks

# Configure filesystems with JSON payload
python3 /tmp/set-disks.py --payload '<JSON_PAYLOAD>'

# Verify existing configuration
python3 /tmp/set-disks.py --verify
```

## Testing Scenarios

### 1. List Available Disks

```bash
# List unmounted disks available for configuration
sudo python3 /tmp/set-disks.py --list-disks
```

**Expected Output:**
```json
[
    {
        "disk": "/dev/sdb",
        "partitioned": false,
        "mounted": false,
        "actions": ["unmounted and no existing partitions"]
    },
    {
        "disk": "/dev/sdc", 
        "partitioned": false,
        "mounted": false,
        "actions": ["unmounted and no existing partitions"]
    }
]
```

### 2. Configure Single Filesystem

```bash
# Configure one filesystem
sudo python3 /tmp/set-disks.py --payload '[{"Data Drive Name": "/opt/application"}]'
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] INFO: Starting disk management for hostname: testserver
[2024-01-15T10:30:45.234567] INFO: Processing payload with 1 filesystem requests
[2024-01-15T10:30:45.345678] INFO: Processing disk /dev/sdb for mount point /opt/application
[2024-01-15T10:30:45.456789] INFO: Generated LV name: testserver_sdb_lv, VG name: testserver_sdb_vg
[2024-01-15T10:30:45.567890] INFO: Formatting disk /dev/sdb
[2024-01-15T10:30:45.678901] INFO: Successfully formatted disk /dev/sdb
[2024-01-15T10:30:45.789012] INFO: Creating mount point /opt/application
[2024-01-15T10:30:45.890123] INFO: Created mount point: /opt/application
[2024-01-15T10:30:45.901234] INFO: Creating partition on /dev/sdb and logical volume testserver_sdb_lv at /opt/application
[2024-01-15T10:30:46.012345] INFO: Successfully created and mounted logical volume testserver_sdb_lv at /opt/application
[2024-01-15T10:30:46.123456] INFO: Added /opt/application to /etc/fstab
[2024-01-15T10:30:46.234567] INFO: Successfully configured disk /dev/sdb with mount point /opt/application
[2024-01-15T10:30:46.345678] INFO: Successfully configured 1 out of 1 filesystems
[2024-01-15T10:30:46.456789] INFO: Filesystem /opt/application verified successfully
[2024-01-15T10:30:46.567890] INFO: All filesystems verified successfully
[2024-01-15T10:30:46.678901] INFO: Disk configuration completed and verified successfully
```

### 3. Configure Multiple Filesystems

```bash
# Configure multiple filesystems
sudo python3 /tmp/set-disks.py --payload '[
    {"Data Drive Name": "/opt/application"},
    {"Data Drive Name": "/var/data"},
    {"Data Drive Name": "/backup"}
]'
```

### 4. Verify Configuration

```bash
# Verify existing filesystem configuration
sudo python3 /tmp/set-disks.py --verify
```

## Validation Tests

### 1. Test Invalid Mount Point

```bash
sudo python3 /tmp/set-disks.py --payload '[{"Data Drive Name": "/etc/invalid"}]'
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Mount point in dangerous location: /etc/invalid
```

### 2. Test Invalid JSON

```bash
sudo python3 /tmp/set-disks.py --payload 'invalid json'
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Invalid JSON payload: Expecting value: line 1 column 1 (char 0)
```

### 3. Test Missing Disks

```bash
# Try to configure more filesystems than available disks
sudo python3 /tmp/set-disks.py --payload '[
    {"Data Drive Name": "/opt/app1"},
    {"Data Drive Name": "/opt/app2"},
    {"Data Drive Name": "/opt/app3"},
    {"Data Drive Name": "/opt/app4"},
    {"Data Drive Name": "/opt/app5"}
]'
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Not enough unmounted disks available. Required: 5, Available: 2
```

## Verification Steps

### 1. Check Disk Partitioning

```bash
# Check partition table
sudo fdisk -l

# Check specific disk partitioning
lsblk /dev/sdb

# Verify partition type
sudo parted /dev/sdb print
```

### 2. Verify LVM Configuration

```bash
# Check physical volumes
sudo pvs

# Check volume groups
sudo vgs

# Check logical volumes
sudo lvs

# Detailed LV information
sudo lvdisplay
```

### 3. Check Filesystems

```bash
# Check mounted filesystems
df -h

# Check filesystem types
mount | grep xfs

# Verify specific mount points
mountpoint /opt/application
mountpoint /var/data
```

### 4. Verify /etc/fstab Entries

```bash
# Check fstab entries
cat /etc/fstab

# Check for specific entries
grep "/opt/application" /etc/fstab
grep "xfs" /etc/fstab

# Test fstab syntax
sudo mount -a
```

### 5. Check Backup Files

```bash
# List fstab backup files
ls -la /etc/fstab.backup.*

# Compare with backup
if ls /etc/fstab.backup.* 1> /dev/null 2>&1; then
    latest_backup=$(ls -t /etc/fstab.backup.* | head -1)
    echo "Comparing with backup: $latest_backup"
    diff "$latest_backup" /etc/fstab
fi
```

## Advanced Testing

### 1. Test Idempotency

```bash
# Run the same configuration twice
sudo python3 /tmp/set-disks.py --payload '[{"Data Drive Name": "/opt/application"}]'
sudo python3 /tmp/set-disks.py --payload '[{"Data Drive Name": "/opt/application"}]'

# Check for duplicate entries in fstab
grep -c "/opt/application" /etc/fstab
```

### 2. Test with Existing Mount Points

```bash
# Create mount point manually
sudo mkdir -p /opt/existing

# Run configuration
sudo python3 /tmp/set-disks.py --payload '[{"Data Drive Name": "/opt/existing"}]'

# Verify it handles existing directories gracefully
```

### 3. Test Different Mount Point Types

```bash
# Test various mount point patterns
test_mount_points=(
    "/opt/app"
    "/data/storage"
    "/backup/daily"
    "/logs/application"
    "/home/<USER>"
)

for mount_point in "${test_mount_points[@]}"; do
    echo "Testing mount point: $mount_point"
    sudo python3 /tmp/set-disks.py --payload "[{\"Data Drive Name\": \"$mount_point\"}]"
    
    if mountpoint -q "$mount_point"; then
        echo "✓ $mount_point mounted successfully"
    else
        echo "✗ $mount_point mount failed"
    fi
    echo "---"
done
```

## Performance Testing

```bash
# Time single filesystem creation
time sudo python3 /tmp/set-disks.py --payload '[{"Data Drive Name": "/opt/test1"}]'

# Time multiple filesystem creation
time sudo python3 /tmp/set-disks.py --payload '[
    {"Data Drive Name": "/opt/test2"},
    {"Data Drive Name": "/opt/test3"}
]'
```

## Error Condition Testing

### 1. Test Without Sudo

```bash
# Should fail with permission error
python3 /tmp/set-disks.py --list-disks
```

### 2. Test with Insufficient Disk Space

```bash
# Check available space on small disk (if available)
lsblk -o NAME,SIZE

# Try to create filesystem on very small disk
# (This test depends on having a small test disk available)
```

### 3. Test with Busy Disk

```bash
# If you have a disk that's in use, test error handling
# (Be careful not to affect system disks)
```

## Filesystem Testing

### 1. Test Filesystem Operations

```bash
# After creating filesystems, test basic operations
for mount_point in /opt/application /var/data /backup; do
    if mountpoint -q "$mount_point"; then
        echo "Testing filesystem operations on $mount_point"
        
        # Test write
        sudo touch "$mount_point/test_file"
        
        # Test permissions
        sudo chmod 755 "$mount_point/test_file"
        
        # Test space
        df -h "$mount_point"
        
        # Clean up
        sudo rm -f "$mount_point/test_file"
        
        echo "✓ $mount_point filesystem operational"
    fi
done
```

### 2. Test XFS Features

```bash
# Check XFS filesystem features
for mount_point in /opt/application /var/data; do
    if mountpoint -q "$mount_point"; then
        device=$(df "$mount_point" | tail -1 | awk '{print $1}')
        echo "XFS info for $mount_point ($device):"
        sudo xfs_info "$mount_point"
        echo "---"
    fi
done
```

## Integration Testing

### Test with JSON File

```bash
# Create test configuration file
cat > /tmp/filesystem-config.json << EOF
[
    {
        "Data Drive Name": "/opt/application"
    },
    {
        "Data Drive Name": "/var/data"
    },
    {
        "Data Drive Name": "/backup"
    }
]
EOF

# Test with file input
payload=$(cat /tmp/filesystem-config.json)
sudo python3 /tmp/set-disks.py --payload "$payload"
```

### Test Configuration Validation

```bash
# Validate the complete configuration
sudo python3 -c "
import json
import subprocess

# Check mount points
result = subprocess.run(['df', '-h'], capture_output=True, text=True)
mount_lines = [line for line in result.stdout.split('\n') if '/opt/' in line or '/var/data' in line or '/backup' in line]

print(f'Configured mount points: {len(mount_lines)}')
for line in mount_lines:
    print(f'  {line}')

# Check LVM
result = subprocess.run(['sudo', 'lvs', '--noheadings'], capture_output=True, text=True)
lv_lines = [line.strip() for line in result.stdout.split('\n') if line.strip()]

print(f'Logical volumes: {len(lv_lines)}')
for line in lv_lines[:5]:  # Show first 5
    print(f'  {line}')
"
```

## Cleanup and Rollback

### 1. Remove Test Filesystems

```bash
# Unmount filesystems
for mount_point in /opt/application /var/data /backup; do
    if mountpoint -q "$mount_point"; then
        sudo umount "$mount_point"
        echo "Unmounted: $mount_point"
    fi
done

# Remove from fstab
sudo sed -i '/\/opt\/application/d' /etc/fstab
sudo sed -i '/\/var\/data/d' /etc/fstab
sudo sed -i '/\/backup/d' /etc/fstab
```

### 2. Remove LVM Configuration

```bash
# Remove logical volumes
sudo lvremove -f /dev/*/testserver_*_lv 2>/dev/null || echo "No LVs to remove"

# Remove volume groups
sudo vgremove -f testserver_*_vg 2>/dev/null || echo "No VGs to remove"

# Remove physical volumes
for disk in /dev/sdb /dev/sdc; do
    if sudo pvs | grep -q "$disk"; then
        sudo pvremove "$disk"
        echo "Removed PV: $disk"
    fi
done
```

### 3. Clean Disk Partitions

```bash
# Remove partition tables (BE VERY CAREFUL)
for disk in /dev/sdb /dev/sdc; do
    if [ -b "$disk" ]; then
        sudo wipefs -a "$disk"
        echo "Wiped partition table: $disk"
    fi
done
```

### 4. Remove Mount Points

```bash
# Remove created mount points
for mount_point in /opt/application /var/data /backup; do
    if [ -d "$mount_point" ]; then
        sudo rmdir "$mount_point" 2>/dev/null && echo "Removed: $mount_point" || echo "Directory not empty: $mount_point"
    fi
done
```

### 5. Restore fstab

```bash
# Restore from backup if needed
if ls /etc/fstab.backup.* 1> /dev/null 2>&1; then
    latest_backup=$(ls -t /etc/fstab.backup.* | head -1)
    sudo cp "$latest_backup" /etc/fstab
    echo "Restored fstab from: $latest_backup"
fi

# Remove backup files
sudo rm -f /etc/fstab.backup.*
```

## Troubleshooting

### Common Issues

1. **No Available Disks**
   ```bash
   # Check for unmounted disks
   lsblk -d -o NAME,TYPE,SIZE,MOUNTPOINT | grep disk
   
   # Add virtual disks for testing (if using VM)
   # This depends on your virtualization platform
   ```

2. **LVM Tools Missing**
   ```bash
   # Install required packages
   sudo yum install lvm2 xfsprogs parted
   ```

3. **Permission Denied**
   ```bash
   # Ensure running with sudo
   sudo python3 /tmp/set-disks.py --list-disks
   ```

4. **Mount Point Creation Failed**
   ```bash
   # Check parent directory permissions
   ls -la /opt/
   
   # Check filesystem space
   df -h /
   ```

5. **Partition Creation Failed**
   ```bash
   # Check if disk is in use
   lsof | grep /dev/sdb
   
   # Check disk health
   sudo smartctl -H /dev/sdb
   ```

## Best Practices for Local Testing

1. **Use Test Disks**: Only use disks designated for testing
2. **Backup Important Data**: Never test on disks with important data
3. **Start Small**: Test with one filesystem before multiple
4. **Verify Each Step**: Check partitioning, LVM, and mounting separately
5. **Test Rollback**: Always test the cleanup procedures
6. **Document Changes**: Keep track of what disks and mount points are used
7. **Use VMs**: Prefer testing in virtual machines with disposable disks

## Security Considerations

- Script requires root privileges for disk operations
- Validates mount point paths to prevent dangerous locations
- Creates backups of /etc/fstab before modifications
- Uses safe disk operations with proper error checking
- Validates JSON payload structure to prevent injection attacks

## Expected Results

After successful execution:
- New partitions created on specified disks
- LVM physical volumes, volume groups, and logical volumes configured
- XFS filesystems created and mounted
- Mount points added to /etc/fstab
- All filesystems accessible and operational

**Warning**: Disk operations are destructive. Always test on non-production systems with disposable disks.
