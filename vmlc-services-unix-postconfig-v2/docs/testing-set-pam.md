# Testing set-pam.py Script Locally

This document explains how to test the `set-pam.py` script directly on RedHat/Oracle Linux servers for PAM account management validation.

## Overview

The `set-pam.py` script manages:
- PAM user accounts for different environments (production, staging, development)
- User groups (ihisadm, pamunixread, pamunixdeadm, pamunixderead)
- Password policies for pamunix accounts
- User creation and deletion operations

## Prerequisites

### System Requirements
- RedHat Enterprise Linux 7/8/9 or Oracle Linux 7/8/9
- Python 3.6 or higher
- Root or sudo privileges
- Standard user management tools (useradd, userdel, groupadd, etc.)

### Check Prerequisites
```bash
# Check Python version
python3 --version

# Check user management tools
which useradd userdel groupadd groupdel chage

# Check sudo privileges
sudo -l

# Check existing users and groups
getent passwd | grep pamunix
getent group | grep -E "(ihisadm|pamunix)"
```

## Script Location and Permissions

```bash
# Copy the script to test location
cp /path/to/set-pam.py /tmp/set-pam.py

# Make executable
chmod +x /tmp/set-pam.py

# Verify permissions
ls -la /tmp/set-pam.py
```

## Usage Syntax

```bash
python3 /tmp/set-pam.py <environment> [action]
```

### Parameters
- **environment**: `production`, `staging`, or `development`
- **action**: `add` (default) or `delete`

## Testing Scenarios

### 1. Production Environment - Add Users

```bash
# Add production PAM accounts
sudo python3 /tmp/set-pam.py production add
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] INFO: Starting PAM accounts management: environment=production, action=add
[2024-01-15T10:30:45.234567] INFO: Successfully created group ihisadm
[2024-01-15T10:30:45.345678] INFO: Successfully created user pamunixadm1001 with primary group ihisadm
[2024-01-15T10:30:45.456789] INFO: Successfully created user pamunixadm1002 with primary group ihisadm
[2024-01-15T10:30:45.567890] INFO: Successfully created group pamunixread
[2024-01-15T10:30:45.678901] INFO: Successfully created user pamunixread1001 with primary group pamunixread
[2024-01-15T10:30:45.789012] INFO: Successfully changed min days for pamunixadm1001
[2024-01-15T10:30:45.890123] INFO: PAM accounts management completed successfully
```

### 2. Staging Environment - Add Users

```bash
# Add staging PAM accounts
sudo python3 /tmp/set-pam.py staging add
```

**Expected Users Created:**
- ihisadm group: pamunixadm2001-2006
- pamunixread group: pamunixread2001
- pamunixdeadm group: pamunixadm2011-2012
- pamunixderead group: pamunixread2011-2012

### 3. Development Environment - Add Users

```bash
# Add development PAM accounts
sudo python3 /tmp/set-pam.py development add
```

**Expected Users Created:**
- ihisadm group: pamunixadm3001-3002
- pamunixread group: pamunixread3001
- pamunixdeadm group: pamunixadm3011
- pamunixderead group: pamunixread3011

### 4. Delete Users Test

```bash
# Delete staging users
sudo python3 /tmp/set-pam.py staging delete
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] INFO: Starting PAM accounts management: environment=staging, action=delete
[2024-01-15T10:30:45.234567] INFO: Successfully deleted user pamunixadm2001
[2024-01-15T10:30:45.345678] INFO: Successfully deleted user pamunixadm2002
[2024-01-15T10:30:45.456789] INFO: Skipping deletion of protected group ihisadm
[2024-01-15T10:30:45.567890] INFO: PAM accounts management completed successfully
```

## Validation Tests

### 1. Test Invalid Environment

```bash
sudo python3 /tmp/set-pam.py invalid_env add
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Invalid environment: invalid_env. Must be staging, production, or development
```

### 2. Test Invalid Action

```bash
sudo python3 /tmp/set-pam.py production invalid_action
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Invalid action: invalid_action. Must be add or delete
```

### 3. Test Missing Parameters

```bash
sudo python3 /tmp/set-pam.py
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Usage: python set-pam.py <environment> [action]
[2024-01-15T10:30:45.234567] ERROR: Environment: staging, production, development
[2024-01-15T10:30:45.345678] ERROR: Action: add (default), delete
```

## Verification Steps

### 1. Check Created Users

```bash
# List all pamunix users
getent passwd | grep pamunix | sort

# Check specific environment users
getent passwd | grep pamunixadm1001  # Production
getent passwd | grep pamunixadm2001  # Staging
getent passwd | grep pamunixadm3001  # Development
```

### 2. Verify Groups

```bash
# List all relevant groups
getent group | grep -E "(ihisadm|pamunix)" | sort

# Check group membership
groups pamunixadm1001
groups pamunixread1001
```

### 3. Check User Properties

```bash
# Check user details
id pamunixadm1001

# Check home directories
ls -la /home/<USER>

# Check password aging
chage -l pamunixadm1001
```

### 4. Verify Password Policies

```bash
# Check min days setting (should be 0)
chage -l pamunixadm1001 | grep "Minimum"

# Check all pamunix accounts
for user in $(getent passwd | grep pamunix | cut -d: -f1); do
    echo "User: $user"
    chage -l $user | grep "Minimum"
done
```

## Advanced Testing

### 1. Test Idempotency

```bash
# Run the same command twice
sudo python3 /tmp/set-pam.py production add
sudo python3 /tmp/set-pam.py production add
```

**Expected Output (Second Run):**
```
[2024-01-15T10:30:45.123456] INFO: Group ihisadm already exists
[2024-01-15T10:30:45.234567] INFO: User pamunixadm1001 already exists
[2024-01-15T10:30:45.345678] INFO: All pamunix accounts already have min days set to 0
```

### 2. Test Partial Failure Recovery

```bash
# Create a user manually to test conflict handling
sudo useradd pamunixadm1001

# Run script (should handle existing user gracefully)
sudo python3 /tmp/set-pam.py production add
```

### 3. Test Different Environments Simultaneously

```bash
# Add users for all environments
sudo python3 /tmp/set-pam.py production add
sudo python3 /tmp/set-pam.py staging add
sudo python3 /tmp/set-pam.py development add

# Verify no conflicts
getent passwd | grep pamunix | wc -l
```

## Environment-Specific Testing

### Production Environment Test

```bash
# Test production accounts
sudo python3 /tmp/set-pam.py production add

# Verify production users (1001-1006, 1011-1012)
getent passwd | grep pamunixadm100[1-6]
getent passwd | grep pamunixadm101[1-2]
getent passwd | grep pamunixread100[1]
getent passwd | grep pamunixread101[1-2]
```

### Staging Environment Test

```bash
# Test staging accounts
sudo python3 /tmp/set-pam.py staging add

# Verify staging users (2001-2006, 2011-2012)
getent passwd | grep pamunixadm200[1-6]
getent passwd | grep pamunixadm201[1-2]
getent passwd | grep pamunixread200[1]
getent passwd | grep pamunixread201[1-2]
```

### Development Environment Test

```bash
# Test development accounts
sudo python3 /tmp/set-pam.py development add

# Verify development users (3001-3002, 3011)
getent passwd | grep pamunixadm300[1-2]
getent passwd | grep pamunixadm301[1]
getent passwd | grep pamunixread300[1]
getent passwd | grep pamunixread301[1]
```

## Performance Testing

```bash
# Time the execution
time sudo python3 /tmp/set-pam.py production add

# Test with multiple environments
time (
    sudo python3 /tmp/set-pam.py production add
    sudo python3 /tmp/set-pam.py staging add
    sudo python3 /tmp/set-pam.py development add
)
```

## Error Condition Testing

### 1. Test Without Sudo

```bash
# Should fail with permission error
python3 /tmp/set-pam.py production add
```

### 2. Test with Insufficient Disk Space

```bash
# Check available space
df -h /home

# If testing on a system with limited space, monitor during execution
```

### 3. Test with Existing Conflicting Users

```bash
# Create a conflicting user with different group
sudo useradd -g users pamunixadm1001

# Run script (should handle gracefully)
sudo python3 /tmp/set-pam.py production add
```

## Cleanup and Rollback

### 1. Remove Test Users

```bash
# Remove all pamunix users
for user in $(getent passwd | grep pamunix | cut -d: -f1); do
    sudo userdel -r $user
done

# Or use the script's delete function
sudo python3 /tmp/set-pam.py production delete
sudo python3 /tmp/set-pam.py staging delete
sudo python3 /tmp/set-pam.py development delete
```

### 2. Remove Test Groups

```bash
# Remove non-protected groups (script protects standard groups)
sudo groupdel pamunixread 2>/dev/null || true
sudo groupdel pamunixdeadm 2>/dev/null || true
sudo groupdel pamunixderead 2>/dev/null || true

# Note: ihisadm group is protected and won't be deleted by script
```

### 3. Verify Cleanup

```bash
# Check no pamunix users remain
getent passwd | grep pamunix

# Check remaining groups
getent group | grep -E "(ihisadm|pamunix)"
```

## Integration Testing

### Test with Ansible Variables

```bash
# Simulate Ansible variable passing
ENVIRONMENT="production"
PAM_ACTION="add"

sudo python3 /tmp/set-pam.py "$ENVIRONMENT" "$PAM_ACTION"
```

### Test Configuration Verification

```bash
# After running script, verify configuration
sudo python3 -c "
import subprocess
result = subprocess.run(['getent', 'passwd'], capture_output=True, text=True)
pamunix_users = [line for line in result.stdout.split('\n') if 'pamunix' in line]
print(f'Found {len(pamunix_users)} pamunix users')
for user in pamunix_users[:5]:  # Show first 5
    print(user)
"
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Ensure running with sudo
   sudo python3 /tmp/set-pam.py production add
   ```

2. **User Already Exists**
   ```bash
   # Check existing users
   getent passwd | grep pamunix
   
   # Remove conflicting users if needed
   sudo userdel conflicting_user
   ```

3. **Group Creation Failed**
   ```bash
   # Check if group already exists
   getent group groupname
   
   # Check system limits
   cat /etc/login.defs | grep -E "(UID_|GID_)"
   ```

4. **Home Directory Issues**
   ```bash
   # Check /home permissions
   ls -la /home/
   
   # Check disk space
   df -h /home
   ```

## Best Practices for Local Testing

1. **Start Small**: Test with development environment first
2. **Verify Each Step**: Check users and groups after each operation
3. **Test Rollback**: Always test the delete functionality
4. **Document Changes**: Keep track of what users/groups are created
5. **Use Test Systems**: Don't test on production systems
6. **Check Dependencies**: Verify all required tools are available

## Security Considerations

- Script requires root privileges for user management
- Creates users with proper group assignments
- Sets appropriate password policies
- Validates input parameters to prevent injection
- Handles existing users gracefully without overwriting
