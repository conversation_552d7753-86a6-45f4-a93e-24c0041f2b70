# Local Testing Guide for Unix Post-Configuration Scripts

This directory contains comprehensive testing documentation for each Python script in the Unix Post-Configuration v2 project. These guides help you test each component individually on RedHat/Oracle Linux servers before deploying through Ansible.

## Overview

The v2 project includes enhanced Python scripts with improved error handling, validation, and security. Each script can be tested independently to validate functionality and troubleshoot issues.

## Testing Documentation

### Individual Script Testing Guides

| Script | Purpose | Documentation | Key Features |
|--------|---------|---------------|--------------|
| `set-host.py` | Hostname & /etc/hosts configuration | [testing-set-host.md](testing-set-host.md) | Hostname validation, hosts file management, NetBackup entries |
| `set-pam.py` | PAM accounts management | [testing-set-pam.md](testing-set-pam.md) | User/group creation, environment-specific accounts, password policies |
| `set-bpconf.py` | NetBackup configuration | [testing-set-bpconf.md](testing-set-bpconf.md) | bp.conf management, server entries, client configuration |
| `set-chrony.py` | NTP time synchronization | [testing-set-chrony.md](testing-set-chrony.md) | Data center-aware NTP servers, chrony.conf management |
| `set-resolv.py` | DNS resolution configuration | [testing-set-resolv.md](testing-set-resolv.md) | Domain-specific DNS, data center prioritization |
| `set-disks.py` | Disk & filesystem management | [testing-set-disks.md](testing-set-disks.md) | LVM setup, XFS filesystems, mount point management |

## Quick Start Testing

### Prerequisites for All Scripts

```bash
# Check system requirements
cat /etc/redhat-release
python3 --version
sudo -l

# Install common dependencies
sudo yum install python3 -y  # RHEL/Oracle Linux 7
# or
sudo dnf install python3 -y  # RHEL/Oracle Linux 8/9
```

### Basic Test Workflow

1. **Copy Script to Test Location**
   ```bash
   cp /path/to/script.py /tmp/script.py
   chmod +x /tmp/script.py
   ```

2. **Run Basic Validation Test**
   ```bash
   # Test with invalid parameters to check validation
   sudo python3 /tmp/script.py invalid_params
   ```

3. **Run Actual Configuration Test**
   ```bash
   # Follow specific script documentation for proper parameters
   sudo python3 /tmp/script.py proper_params
   ```

4. **Verify Results**
   ```bash
   # Check configuration files and system state
   # Specific verification steps in each script's documentation
   ```

5. **Cleanup**
   ```bash
   # Restore original configuration if needed
   # Remove test files and reset system state
   ```

## Common Testing Patterns

### Input Validation Testing

All scripts include comprehensive input validation. Test with:

```bash
# Missing parameters
sudo python3 /tmp/script.py

# Invalid parameters
sudo python3 /tmp/script.py invalid_input

# Malformed input
sudo python3 /tmp/script.py "malformed..input"
```

### Permission Testing

```bash
# Test without sudo (should fail gracefully)
python3 /tmp/script.py valid_params

# Test with sudo (should work)
sudo python3 /tmp/script.py valid_params
```

### Idempotency Testing

```bash
# Run the same configuration twice
sudo python3 /tmp/script.py params
sudo python3 /tmp/script.py params

# Verify no duplicate entries or errors
```

### Backup and Recovery Testing

```bash
# Check backup files are created
ls -la /etc/*.backup.*

# Test restoration from backup
sudo cp /etc/file.backup.timestamp /etc/file
```

## Environment-Specific Testing

### Production-like Testing

```bash
# Use production-like parameters
FQDN="testprod01.hcloud.healthgrp.com.sg"
IP="*************"
ENV="production"
DC="HDC2"

# Test each script with production parameters
sudo python3 /tmp/set-host.py "$FQDN" "$IP"
sudo python3 /tmp/set-pam.py "$ENV" add
sudo python3 /tmp/set-chrony.py "$DC"
sudo python3 /tmp/set-resolv.py "$FQDN" "$DC"
```

### Staging Testing

```bash
# Use staging parameters
FQDN="teststg01.hcloud.healthgrp.com.sg"
IP="**************"
ENV="staging"
DC="HDC1"

# Test with staging configuration
sudo python3 /tmp/set-pam.py "$ENV" add
```

### Development Testing

```bash
# Use development parameters
FQDN="testdev01.devhealthgrp.com.sg"
IP="**************"
ENV="development"
DC="HDC1"

# Test with development configuration
sudo python3 /tmp/set-pam.py "$ENV" add
```

## Integration Testing

### Test Script Interactions

```bash
# Test complete configuration sequence
sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************
sudo python3 /tmp/set-pam.py production add
sudo python3 /tmp/set-bpconf.py
sudo python3 /tmp/set-chrony.py HDC2
sudo python3 /tmp/set-resolv.py testserver.hcloud.healthgrp.com.sg HDC2

# Verify all configurations work together
```

### Test with Ansible Variables

```bash
# Simulate Ansible variable passing
export FQDN="testserver.hcloud.healthgrp.com.sg"
export PRD_IP="*************"
export Environment="production"
export dc_location="HDC2"

# Test scripts with environment variables
sudo python3 /tmp/set-host.py "$FQDN" "$PRD_IP"
sudo python3 /tmp/set-pam.py "$Environment" add
sudo python3 /tmp/set-chrony.py "$dc_location"
sudo python3 /tmp/set-resolv.py "$FQDN" "$dc_location"
```

## Performance Testing

### Timing Tests

```bash
# Time individual script execution
time sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************

# Time complete configuration
time (
    sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************
    sudo python3 /tmp/set-pam.py production add
    sudo python3 /tmp/set-bpconf.py
    sudo python3 /tmp/set-chrony.py HDC2
    sudo python3 /tmp/set-resolv.py testserver.hcloud.healthgrp.com.sg HDC2
)
```

### Resource Usage

```bash
# Monitor resource usage during execution
top -p $(pgrep python3) &
sudo python3 /tmp/set-disks.py --payload '[{"Data Drive Name": "/opt/test"}]'
```

## Error Condition Testing

### Network Issues

```bash
# Test with network connectivity issues
# (Affects DNS resolution and NTP server connectivity)
sudo iptables -A OUTPUT -p udp --dport 53 -j DROP  # Block DNS
sudo python3 /tmp/set-resolv.py testserver.hcloud.healthgrp.com.sg HDC2
sudo iptables -D OUTPUT -p udp --dport 53 -j DROP  # Restore DNS
```

### Filesystem Issues

```bash
# Test with read-only filesystem (where possible)
sudo mount -o remount,ro /etc 2>/dev/null || echo "Cannot remount /etc as read-only"
sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************
sudo mount -o remount,rw /etc 2>/dev/null || echo "Filesystem not remounted"
```

### Resource Constraints

```bash
# Test with limited disk space (for disk script)
df -h
sudo python3 /tmp/set-disks.py --list-disks
```

## Troubleshooting Common Issues

### Python Environment

```bash
# Check Python installation
which python3
python3 --version

# Check required modules
python3 -c "import subprocess, json, os, sys, socket, argparse, re, datetime"
```

### System Dependencies

```bash
# Check required system tools
which hostnamectl useradd groupadd lsblk pvs chronyd nslookup

# Install missing tools
sudo yum install systemd util-linux lvm2 chrony bind-utils
```

### Permission Issues

```bash
# Check sudo configuration
sudo -l

# Verify file permissions
ls -la /etc/hosts /etc/chrony.conf /etc/resolv.conf /etc/fstab
```

### Service Dependencies

```bash
# Check required services
systemctl status chronyd
systemctl status NetworkManager

# Start services if needed
sudo systemctl start chronyd
sudo systemctl enable chronyd
```

## Best Practices for Local Testing

### Safety Guidelines

1. **Use Test Systems**: Never test on production servers
2. **Backup First**: Always backup configuration files before testing
3. **Test Incrementally**: Test one script at a time before integration testing
4. **Verify Results**: Always check configuration files and system state after testing
5. **Document Issues**: Keep notes of any problems encountered
6. **Clean Up**: Restore original configuration after testing

### Test Environment Setup

1. **Virtual Machines**: Use VMs with snapshots for easy rollback
2. **Test Data**: Use realistic but non-production hostnames and IPs
3. **Network Isolation**: Ensure test systems don't affect production networks
4. **Monitoring**: Monitor system resources during testing
5. **Logging**: Enable debug output for troubleshooting

### Validation Checklist

After testing each script:

- [ ] Configuration files are correctly modified
- [ ] Backup files are created
- [ ] System services are functioning
- [ ] No error messages in logs
- [ ] Idempotency works correctly
- [ ] Rollback procedures work
- [ ] Integration with other scripts works

## Getting Help

### Documentation References

- Individual script testing guides in this directory
- Main project README: `../README.md`
- Upgrade guide: `../UPGRADE_GUIDE.md`
- Dynamic host example: `../examples/dynamic-host-example.md`

### Troubleshooting Resources

1. **Script Logs**: Check script output for detailed error messages
2. **System Logs**: Check `/var/log/messages` and service-specific logs
3. **Configuration Files**: Verify syntax and content of modified files
4. **Network Connectivity**: Test connectivity to required services
5. **System Resources**: Check disk space, memory, and CPU usage

### Support Contacts

- CES Operational Excellence Team for script-related issues
- System administrators for infrastructure-related problems
- Network team for connectivity issues

## Summary

This testing framework provides comprehensive validation for all Unix post-configuration scripts. Use the individual script guides for detailed testing procedures, and follow the common patterns outlined here for consistent testing across all components.

Remember: **Always test in non-production environments first!**
