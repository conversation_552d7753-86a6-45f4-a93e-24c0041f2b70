# Testing set-chrony.py Script Locally

This document explains how to test the `set-chrony.py` script directly on RedHat/Oracle Linux servers for NTP time synchronization configuration.

## Overview

The `set-chrony.py` script configures:
- Chrony NTP client configuration (`/etc/chrony.conf`)
- Data center-specific NTP server entries (HDC1/HDC2)
- Proper NTP server prioritization based on location
- Backup and integration with existing configuration

## Prerequisites

### System Requirements
- RedHat Enterprise Linux 7/8/9 or Oracle Linux 7/8/9
- Python 3.6 or higher
- Root or sudo privileges
- Chrony package installed (optional for testing)

### Check Prerequisites
```bash
# Check Python version
python3 --version

# Check if chrony is installed
rpm -qa | grep chrony

# Check current chrony configuration
ls -la /etc/chrony.conf

# Check chrony service status
systemctl status chronyd 2>/dev/null || echo "Chrony service not running"

# Check sudo privileges
sudo -l
```

### Install Chrony (if needed for testing)
```bash
# RHEL/Oracle Linux 7/8/9
sudo yum install chrony -y
# or
sudo dnf install chrony -y
```

## Script Location and Permissions

```bash
# Copy the script to test location
cp /path/to/set-chrony.py /tmp/set-chrony.py

# Make executable
chmod +x /tmp/set-chrony.py

# Verify permissions
ls -la /tmp/set-chrony.py
```

## Usage Syntax

```bash
python3 /tmp/set-chrony.py <HDC1|HDC2>
```

### Parameters
- **HDC1**: Configure for HDC1 AMK data center (primary: AMK servers, secondary: FORT servers)
- **HDC2**: Configure for HDC2 FORT data center (primary: FORT servers, secondary: AMK servers)

## Testing Scenarios

### 1. HDC1 Configuration Test

```bash
# Configure for HDC1 (AMK data center)
sudo python3 /tmp/set-chrony.py HDC1
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] INFO: Starting Chrony configuration for HDC1
[2024-01-15T10:30:45.234567] INFO: Backed up existing chrony.conf to /etc/chrony.conf.backup.20240115_103045
[2024-01-15T10:30:45.345678] INFO: Successfully updated chrony.conf for HDC1
[2024-01-15T10:30:45.456789] INFO: chrony.conf verification successful: 4 NTP servers configured
[2024-01-15T10:30:45.567890] INFO: Chrony configuration syntax test passed
[2024-01-15T10:30:45.678901] INFO: Chrony configuration completed successfully
```

**Expected NTP Server Order (HDC1):**
1. *********** (HDC1 AMK - Primary)
2. *********** (HDC1 AMK - Primary)
3. ************ (HDC2 FORT - Secondary)
4. ************ (HDC2 FORT - Secondary)

### 2. HDC2 Configuration Test

```bash
# Configure for HDC2 (FORT data center)
sudo python3 /tmp/set-chrony.py HDC2
```

**Expected NTP Server Order (HDC2):**
1. ************ (HDC2 FORT - Primary)
2. ************ (HDC2 FORT - Primary)
3. *********** (HDC1 AMK - Secondary)
4. *********** (HDC1 AMK - Secondary)

## Validation Tests

### 1. Test Invalid Data Center

```bash
sudo python3 /tmp/set-chrony.py INVALID
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Invalid DC location: INVALID. Must be HDC1 or HDC2
```

### 2. Test Missing Parameter

```bash
sudo python3 /tmp/set-chrony.py
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Usage: python set-chrony.py <HDC1|HDC2>
```

### 3. Test Case Sensitivity

```bash
# Test lowercase (should work)
sudo python3 /tmp/set-chrony.py hdc1

# Test mixed case (should work)
sudo python3 /tmp/set-chrony.py Hdc2
```

## Verification Steps

### 1. Check Chrony Configuration File

```bash
# View the complete chrony.conf
cat /etc/chrony.conf

# Check for NTP server entries
grep "server " /etc/chrony.conf

# Verify HDC1 configuration
grep -A 4 "HDC1 AMK NTP servers" /etc/chrony.conf

# Verify HDC2 configuration  
grep -A 4 "HDC2 FORT NTP servers" /etc/chrony.conf
```

### 2. Verify Server Order and Configuration

```bash
# For HDC1 configuration
if grep -q "HDC1 AMK NTP servers" /etc/chrony.conf; then
    echo "HDC1 servers found first (correct for HDC1)"
    grep -A 2 "HDC1 AMK NTP servers" /etc/chrony.conf
else
    echo "HDC1 servers not found or not first"
fi

# For HDC2 configuration
if grep -q "HDC2 FORT NTP servers" /etc/chrony.conf; then
    echo "HDC2 servers found first (correct for HDC2)"
    grep -A 2 "HDC2 FORT NTP servers" /etc/chrony.conf
else
    echo "HDC2 servers not found or not first"
fi
```

### 3. Check Backup Files

```bash
# List backup files
ls -la /etc/chrony.conf.backup.*

# Compare with backup (if exists)
if ls /etc/chrony.conf.backup.* 1> /dev/null 2>&1; then
    echo "Backup files found:"
    ls -la /etc/chrony.conf.backup.*
    
    # Show differences
    diff /etc/chrony.conf.backup.* /etc/chrony.conf 2>/dev/null || echo "No backup to compare"
fi
```

### 4. Test Chrony Syntax (if chrony is installed)

```bash
# Test configuration syntax
if command -v chronyd &> /dev/null; then
    sudo chronyd -t -f /etc/chrony.conf
    echo "Syntax test result: $?"
else
    echo "Chrony not installed - syntax test skipped"
fi
```

## Advanced Testing

### 1. Test with Existing Configuration

```bash
# Create a test chrony.conf with existing content
sudo tee /etc/chrony.conf << EOF
# Existing configuration
driftfile /var/lib/chrony/drift
makestep 1.0 3
rtcsync
logdir /var/log/chrony

# Existing NTP servers (will be replaced)
server old.ntp.server iburst
server another.old.server iburst

# Other settings (should be preserved)
local stratum 10
EOF

# Run script
sudo python3 /tmp/set-chrony.py HDC1

# Verify existing non-NTP settings are preserved
grep "driftfile" /etc/chrony.conf
grep "makestep" /etc/chrony.conf
grep "local stratum" /etc/chrony.conf

# Verify old NTP servers are removed
grep "old.ntp.server" /etc/chrony.conf || echo "Old servers removed correctly"
```

### 2. Test Idempotency

```bash
# Run the same configuration twice
sudo python3 /tmp/set-chrony.py HDC1
sudo python3 /tmp/set-chrony.py HDC1

# Verify no duplicate entries
server_count=$(grep -c "server 10.247" /etc/chrony.conf)
echo "NTP server entries: $server_count (should be 4)"

# Check for duplicate comments
comment_count=$(grep -c "HDC1 AMK NTP servers" /etc/chrony.conf)
echo "HDC1 comment entries: $comment_count (should be 1)"
```

### 3. Test Configuration Switching

```bash
# Configure for HDC1
sudo python3 /tmp/set-chrony.py HDC1
grep -A 2 "HDC1 AMK" /etc/chrony.conf

# Switch to HDC2
sudo python3 /tmp/set-chrony.py HDC2
grep -A 2 "HDC2 FORT" /etc/chrony.conf

# Verify HDC1 servers are now secondary
grep -A 2 "HDC1 AMK" /etc/chrony.conf
```

## Service Integration Testing

### 1. Test with Chrony Service

```bash
# If chrony is installed and running
if systemctl is-active chronyd &>/dev/null; then
    echo "Chrony service is running"
    
    # Check current sources before change
    chronyc sources
    
    # Apply configuration
    sudo python3 /tmp/set-chrony.py HDC1
    
    # Restart service to apply changes
    sudo systemctl restart chronyd
    
    # Check new sources
    sleep 5
    chronyc sources
    
    # Check synchronization status
    chronyc tracking
else
    echo "Chrony service not running - configuration file only test"
fi
```

### 2. Test NTP Server Connectivity

```bash
# Test connectivity to configured NTP servers
for server in *********** *********** ************ ************; do
    echo "Testing connectivity to $server:123"
    timeout 5 bash -c "</dev/tcp/$server/123" 2>/dev/null && echo "✓ $server reachable" || echo "✗ $server unreachable"
done
```

## Performance Testing

```bash
# Time the execution
time sudo python3 /tmp/set-chrony.py HDC1

# Test multiple switches
time (
    sudo python3 /tmp/set-chrony.py HDC1
    sudo python3 /tmp/set-chrony.py HDC2
    sudo python3 /tmp/set-chrony.py HDC1
)
```

## Error Condition Testing

### 1. Test Without Sudo

```bash
# Should fail with permission error
python3 /tmp/set-chrony.py HDC1
```

### 2. Test with Read-Only Filesystem

```bash
# Make /etc read-only (if possible)
sudo mount -o remount,ro /etc 2>/dev/null || echo "Cannot remount /etc as read-only"

# Try to run script
sudo python3 /tmp/set-chrony.py HDC1

# Restore write access
sudo mount -o remount,rw /etc 2>/dev/null || echo "Filesystem not remounted"
```

### 3. Test with Corrupted Configuration

```bash
# Create invalid chrony.conf
echo "invalid configuration line" | sudo tee /etc/chrony.conf

# Run script (should handle gracefully)
sudo python3 /tmp/set-chrony.py HDC1

# Check if configuration was fixed
cat /etc/chrony.conf
```

## Configuration Validation

### 1. Verify Complete Configuration

```bash
# Check all expected elements are present
expected_elements=(
    "server *********** iburst"
    "server *********** iburst" 
    "server ************ iburst"
    "server ************ iburst"
)

echo "Checking configuration elements:"
for element in "${expected_elements[@]}"; do
    if grep -q "$element" /etc/chrony.conf; then
        echo "✓ Found: $element"
    else
        echo "✗ Missing: $element"
    fi
done
```

### 2. Validate Server Order for Each DC

```bash
# Function to check server order
check_server_order() {
    local dc=$1
    echo "Checking server order for $dc:"
    
    # Get line numbers of server entries
    grep -n "server 10.247" /etc/chrony.conf | while read line; do
        echo "  $line"
    done
}

# Test HDC1 configuration
sudo python3 /tmp/set-chrony.py HDC1
check_server_order "HDC1"

# Test HDC2 configuration  
sudo python3 /tmp/set-chrony.py HDC2
check_server_order "HDC2"
```

## Integration Testing

### Test with Ansible Variables

```bash
# Simulate Ansible variable passing
DC_LOCATION="HDC1"
sudo python3 /tmp/set-chrony.py "$DC_LOCATION"

# Verify configuration
grep -A 2 "HDC1 AMK" /etc/chrony.conf
```

### Test Configuration Parsing

```bash
# Parse and validate the configuration
sudo python3 -c "
with open('/etc/chrony.conf', 'r') as f:
    content = f.read()

# Count NTP servers
ntp_servers = [line for line in content.split('\n') if line.startswith('server 10.247')]
print(f'NTP servers configured: {len(ntp_servers)}')

# Check for required servers
required_servers = ['***********', '***********', '************', '************']
configured_servers = [line.split()[1] for line in ntp_servers]

for server in required_servers:
    if server in configured_servers:
        print(f'✓ {server} configured')
    else:
        print(f'✗ {server} missing')
"
```

## Cleanup After Testing

```bash
# Restore original chrony.conf (if backup exists)
if ls /etc/chrony.conf.backup.* 1> /dev/null 2>&1; then
    latest_backup=$(ls -t /etc/chrony.conf.backup.* | head -1)
    sudo cp "$latest_backup" /etc/chrony.conf
    echo "Restored from backup: $latest_backup"
fi

# Remove backup files
sudo rm -f /etc/chrony.conf.backup.*

# Remove test script
rm /tmp/set-chrony.py

# Restart chrony service if it was running
if systemctl is-active chronyd &>/dev/null; then
    sudo systemctl restart chronyd
    echo "Chrony service restarted"
fi
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Ensure running with sudo
   sudo python3 /tmp/set-chrony.py HDC1
   ```

2. **Configuration File Not Found**
   ```bash
   # Create default chrony.conf if missing
   sudo touch /etc/chrony.conf
   ```

3. **Syntax Test Failed**
   ```bash
   # Check chrony installation
   rpm -qa | grep chrony
   
   # Install if missing
   sudo yum install chrony
   ```

4. **NTP Servers Unreachable**
   ```bash
   # Check network connectivity
   ping -c 3 ***********
   
   # Check firewall rules
   sudo iptables -L | grep 123
   ```

## Best Practices for Local Testing

1. **Backup First**: Always backup existing chrony.conf
2. **Test Both DCs**: Test both HDC1 and HDC2 configurations
3. **Verify Service**: Test with actual chrony service if possible
4. **Check Connectivity**: Verify NTP servers are reachable
5. **Test Switching**: Verify switching between DC configurations works
6. **Monitor Logs**: Check chrony logs for any issues

## Expected Configuration Results

### HDC1 Configuration
```
##HDC1 AMK NTP servers
server *********** iburst
server *********** iburst
##HDC2 FORT NTP servers
server ************ iburst
server ************ iburst
```

### HDC2 Configuration
```
##HDC2 FORT NTP servers
server ************ iburst
server ************ iburst
##HDC1 AMK NTP servers
server *********** iburst
server *********** iburst
```

## Security Considerations

- Script requires root privileges to modify system configuration
- Validates DC location parameter to prevent injection
- Creates backups before making changes
- Uses safe file operations with temporary files
- Preserves existing non-NTP configuration settings
