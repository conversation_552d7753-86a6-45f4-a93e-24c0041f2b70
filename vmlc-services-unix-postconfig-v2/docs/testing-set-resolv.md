# Testing set-resolv.py Script Locally

This document explains how to test the `set-resolv.py` script directly on RedHat/Oracle Linux servers for DNS resolution configuration.

## Overview

The `set-resolv.py` script configures:
- DNS resolution settings in `/etc/resolv.conf`
- Domain-specific DNS servers based on FQDN
- Data center-aware DNS server prioritization (HDC1/HDC2)
- Support for multiple domains (healthgrp.com.sg, hcloud.healthgrp.com.sg, etc.)

## Prerequisites

### System Requirements
- RedHat Enterprise Linux 7/8/9 or Oracle Linux 7/8/9
- Python 3.6 or higher
- Root or sudo privileges
- Network connectivity for DNS testing

### Check Prerequisites
```bash
# Check Python version
python3 --version

# Check current DNS configuration
cat /etc/resolv.conf

# Check network connectivity
ping -c 3 *******

# Check nslookup availability
which nslookup

# Check sudo privileges
sudo -l
```

## Script Location and Permissions

```bash
# Copy the script to test location
cp /path/to/set-resolv.py /tmp/set-resolv.py

# Make executable
chmod +x /tmp/set-resolv.py

# Verify permissions
ls -la /tmp/set-resolv.py
```

## Usage Syntax

```bash
python3 /tmp/set-resolv.py <FQDN> <HDC1|HDC2>
```

### Parameters
- **FQDN**: Fully Qualified Domain Name (determines domain and DNS servers)
- **HDC1/HDC2**: Data center location (affects DNS server priority)

## Supported Domains

The script supports these domains with specific DNS configurations:
- `healthgrp.com.sg`
- `sechealthgrp.com.sg`
- `devhealthgrp.com.sg`
- `healthgrpextp.com.sg`
- `hcloud.healthgrp.com.sg`
- `iltc.healthgrp.com.sg`
- `nhg.local`
- `aic.local`

## Testing Scenarios

### 1. Test hcloud.healthgrp.com.sg Domain (HDC1)

```bash
# Configure DNS for hcloud domain at HDC1
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] INFO: Starting DNS configuration for server01.hcloud.healthgrp.com.sg at HDC1
[2024-01-15T10:30:45.234567] INFO: Backed up existing resolv.conf to /etc/resolv.conf.backup.20240115_103045
[2024-01-15T10:30:45.345678] INFO: Successfully updated resolv.conf for server01.hcloud.healthgrp.com.sg with HDC1
[2024-01-15T10:30:45.456789] INFO: resolv.conf verification successful: 4 nameservers configured for hcloud.healthgrp.com.sg
[2024-01-15T10:30:45.567890] INFO: DNS resolution test successful for server01.hcloud.healthgrp.com.sg
[2024-01-15T10:30:45.678901] INFO: DNS configuration completed successfully
```

**Expected DNS Configuration (HDC1):**
```
search hcloud.healthgrp.com.sg
# HDC1 AMK DNS
nameserver *************
nameserver *************
# HDC2 FORT DNS
nameserver *************
nameserver *************
```

### 2. Test hcloud.healthgrp.com.sg Domain (HDC2)

```bash
# Configure DNS for hcloud domain at HDC2
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC2
```

**Expected DNS Configuration (HDC2):**
```
search hcloud.healthgrp.com.sg
# HDC2 FORT DNS
nameserver *************
nameserver *************
# HDC1 AMK DNS
nameserver *************
nameserver *************
```

### 3. Test Different Domains

```bash
# Test healthgrp.com.sg domain
sudo python3 /tmp/set-resolv.py server01.healthgrp.com.sg HDC1

# Test devhealthgrp.com.sg domain
sudo python3 /tmp/set-resolv.py dev01.devhealthgrp.com.sg HDC1

# Test nhg.local domain
sudo python3 /tmp/set-resolv.py server01.nhg.local HDC2
```

## Validation Tests

### 1. Test Invalid FQDN

```bash
sudo python3 /tmp/set-resolv.py "invalid..hostname" HDC1
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Invalid FQDN format: invalid..hostname
```

### 2. Test Invalid Data Center

```bash
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg INVALID
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Invalid DC location: INVALID. Must be HDC1 or HDC2
```

### 3. Test Unsupported Domain

```bash
sudo python3 /tmp/set-resolv.py server01.unsupported.domain HDC1
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Domain not found: unsupported.domain
```

### 4. Test Missing Parameters

```bash
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Usage: python set-resolv.py <FQDN> <HDC1|HDC2>
```

## Verification Steps

### 1. Check resolv.conf Content

```bash
# View the complete resolv.conf
cat /etc/resolv.conf

# Check search domain
grep "search " /etc/resolv.conf

# Check nameserver entries
grep "nameserver " /etc/resolv.conf

# Count nameservers
grep -c "nameserver " /etc/resolv.conf
```

### 2. Verify Domain-Specific Configuration

```bash
# For hcloud.healthgrp.com.sg
if grep -q "search hcloud.healthgrp.com.sg" /etc/resolv.conf; then
    echo "✓ hcloud domain configured"
    grep "nameserver 10.244.152" /etc/resolv.conf || echo "✗ HDC1 DNS missing"
    grep "nameserver 10.245.152" /etc/resolv.conf || echo "✗ HDC2 DNS missing"
fi

# For healthgrp.com.sg
if grep -q "search healthgrp.com.sg" /etc/resolv.conf; then
    echo "✓ healthgrp domain configured"
    grep "nameserver *************" /etc/resolv.conf || echo "✗ HDC1 DNS missing"
    grep "nameserver *************" /etc/resolv.conf || echo "✗ HDC2 DNS missing"
fi
```

### 3. Test DNS Resolution

```bash
# Test domain resolution
nslookup $(hostname -f)

# Test specific domain resolution
nslookup server01.hcloud.healthgrp.com.sg

# Test reverse lookup
nslookup *************

# Test external resolution
nslookup google.com
```

### 4. Check Backup Files

```bash
# List backup files
ls -la /etc/resolv.conf.backup.*

# Compare with backup
if ls /etc/resolv.conf.backup.* 1> /dev/null 2>&1; then
    latest_backup=$(ls -t /etc/resolv.conf.backup.* | head -1)
    echo "Comparing with backup: $latest_backup"
    diff "$latest_backup" /etc/resolv.conf
fi
```

## Advanced Testing

### 1. Test All Supported Domains

```bash
# Test all domains with HDC1
domains=(
    "server01.healthgrp.com.sg"
    "server01.sechealthgrp.com.sg" 
    "dev01.devhealthgrp.com.sg"
    "ext01.healthgrpextp.com.sg"
    "vm01.hcloud.healthgrp.com.sg"
    "test01.iltc.healthgrp.com.sg"
    "host01.nhg.local"
    "sys01.aic.local"
)

for domain in "${domains[@]}"; do
    echo "Testing domain: $domain"
    sudo python3 /tmp/set-resolv.py "$domain" HDC1
    
    # Verify search domain
    expected_domain=$(echo "$domain" | cut -d. -f2-)
    if grep -q "search $expected_domain" /etc/resolv.conf; then
        echo "✓ $expected_domain configured correctly"
    else
        echo "✗ $expected_domain configuration failed"
    fi
    echo "---"
done
```

### 2. Test Data Center Priority

```bash
# Test HDC1 priority
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1

# Check that HDC1 servers come first
hdc1_line=$(grep -n "HDC1 AMK DNS" /etc/resolv.conf | cut -d: -f1)
hdc2_line=$(grep -n "HDC2 FORT DNS" /etc/resolv.conf | cut -d: -f1)

if [ "$hdc1_line" -lt "$hdc2_line" ]; then
    echo "✓ HDC1 servers prioritized correctly"
else
    echo "✗ HDC1 server priority incorrect"
fi

# Test HDC2 priority
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC2

hdc1_line=$(grep -n "HDC1 AMK DNS" /etc/resolv.conf | cut -d: -f1)
hdc2_line=$(grep -n "HDC2 FORT DNS" /etc/resolv.conf | cut -d: -f1)

if [ "$hdc2_line" -lt "$hdc1_line" ]; then
    echo "✓ HDC2 servers prioritized correctly"
else
    echo "✗ HDC2 server priority incorrect"
fi
```

### 3. Test Idempotency

```bash
# Run the same configuration twice
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1

# Check for duplicate entries
search_count=$(grep -c "search hcloud.healthgrp.com.sg" /etc/resolv.conf)
echo "Search domain entries: $search_count (should be 1)"

nameserver_count=$(grep -c "nameserver *************" /etc/resolv.conf)
echo "Duplicate nameserver entries: $nameserver_count (should be 1)"
```

### 4. Test with Existing Configuration

```bash
# Create resolv.conf with existing content
sudo tee /etc/resolv.conf << EOF
# Existing configuration
options timeout:2
options attempts:3
# Existing nameservers (will be replaced)
nameserver *******
nameserver *******
EOF

# Run script
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1

# Verify existing non-DNS settings are preserved
grep "options timeout" /etc/resolv.conf
grep "options attempts" /etc/resolv.conf

# Verify old nameservers are removed
grep "*******" /etc/resolv.conf || echo "Old nameservers removed correctly"
```

## DNS Resolution Testing

### 1. Test Each Configured Nameserver

```bash
# Extract nameservers from resolv.conf
nameservers=$(grep "nameserver " /etc/resolv.conf | awk '{print $2}')

echo "Testing each configured nameserver:"
for ns in $nameservers; do
    echo "Testing nameserver: $ns"
    timeout 5 nslookup google.com $ns > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✓ $ns responding"
    else
        echo "✗ $ns not responding"
    fi
done
```

### 2. Test Domain-Specific Resolution

```bash
# Test internal domain resolution
internal_domains=(
    "hcloud.healthgrp.com.sg"
    "healthgrp.com.sg"
    "nhg.local"
)

for domain in "${internal_domains[@]}"; do
    echo "Testing resolution for domain: $domain"
    nslookup "test.$domain" 2>/dev/null | grep -q "NXDOMAIN" || echo "✓ $domain resolves"
done
```

## Performance Testing

```bash
# Time the execution
time sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1

# Test multiple domain switches
time (
    sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1
    sudo python3 /tmp/set-resolv.py server01.healthgrp.com.sg HDC2
    sudo python3 /tmp/set-resolv.py dev01.devhealthgrp.com.sg HDC1
)
```

## Error Condition Testing

### 1. Test Without Sudo

```bash
# Should fail with permission error
python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1
```

### 2. Test with Read-Only Filesystem

```bash
# Make /etc read-only (if possible)
sudo mount -o remount,ro /etc 2>/dev/null || echo "Cannot remount /etc as read-only"

# Try to run script
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1

# Restore write access
sudo mount -o remount,rw /etc 2>/dev/null || echo "Filesystem not remounted"
```

### 3. Test Network Connectivity

```bash
# Test with network disconnected (if possible)
# This tests the DNS resolution test failure handling
sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1

# Check if configuration was still applied despite resolution test failure
cat /etc/resolv.conf
```

## Domain-Specific Testing

### 1. Test Each Domain Type

```bash
# Function to test domain configuration
test_domain() {
    local fqdn=$1
    local dc=$2
    local expected_domain=$(echo "$fqdn" | cut -d. -f2-)
    
    echo "Testing: $fqdn at $dc"
    sudo python3 /tmp/set-resolv.py "$fqdn" "$dc"
    
    if grep -q "search $expected_domain" /etc/resolv.conf; then
        echo "✓ Domain $expected_domain configured"
    else
        echo "✗ Domain $expected_domain failed"
    fi
    
    nameserver_count=$(grep -c "nameserver " /etc/resolv.conf)
    echo "Nameservers configured: $nameserver_count"
    echo "---"
}

# Test various domains
test_domain "prod01.healthgrp.com.sg" "HDC1"
test_domain "sec01.sechealthgrp.com.sg" "HDC2"
test_domain "app01.hcloud.healthgrp.com.sg" "HDC1"
test_domain "test01.devhealthgrp.com.sg" "HDC2"
```

## Integration Testing

### Test with Ansible Variables

```bash
# Simulate Ansible variable passing
FQDN="server01.hcloud.healthgrp.com.sg"
DC_LOCATION="HDC1"

sudo python3 /tmp/set-resolv.py "$FQDN" "$DC_LOCATION"

# Verify configuration
domain=$(echo "$FQDN" | cut -d. -f2-)
grep "search $domain" /etc/resolv.conf
```

### Test Configuration Validation

```bash
# Parse and validate the configuration
sudo python3 -c "
import re

with open('/etc/resolv.conf', 'r') as f:
    content = f.read()

# Extract search domain
search_lines = [line for line in content.split('\n') if line.startswith('search ')]
nameserver_lines = [line for line in content.split('\n') if line.startswith('nameserver ')]

print(f'Search domains: {len(search_lines)}')
print(f'Nameservers: {len(nameserver_lines)}')

if len(search_lines) == 1 and len(nameserver_lines) >= 2:
    print('✓ Configuration format is correct')
else:
    print('✗ Configuration format has issues')

# Validate nameserver IPs
for line in nameserver_lines:
    ip = line.split()[1]
    if re.match(r'^10\.24[4-7]\.\d+\.\d+$', ip):
        print(f'✓ Valid internal nameserver: {ip}')
    else:
        print(f'? External or unexpected nameserver: {ip}')
"
```

## Cleanup After Testing

```bash
# Restore original resolv.conf (if backup exists)
if ls /etc/resolv.conf.backup.* 1> /dev/null 2>&1; then
    latest_backup=$(ls -t /etc/resolv.conf.backup.* | head -1)
    sudo cp "$latest_backup" /etc/resolv.conf
    echo "Restored from backup: $latest_backup"
fi

# Remove backup files
sudo rm -f /etc/resolv.conf.backup.*

# Remove test script
rm /tmp/set-resolv.py

# Test DNS resolution after restore
nslookup google.com
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Ensure running with sudo
   sudo python3 /tmp/set-resolv.py server01.hcloud.healthgrp.com.sg HDC1
   ```

2. **Domain Not Supported**
   ```bash
   # Check supported domains in script
   grep -A 20 "load_dns_configuration" /tmp/set-resolv.py
   ```

3. **DNS Resolution Failed**
   ```bash
   # Check network connectivity
   ping -c 3 *******
   
   # Check configured nameservers
   for ns in $(grep nameserver /etc/resolv.conf | awk '{print $2}'); do
       ping -c 1 $ns
   done
   ```

4. **Invalid FQDN Format**
   ```bash
   # Verify FQDN format
   echo "server01.hcloud.healthgrp.com.sg" | grep -E '^[a-zA-Z0-9.-]+$'
   ```

## Best Practices for Local Testing

1. **Backup First**: Always backup existing resolv.conf
2. **Test All Domains**: Test each supported domain type
3. **Verify Resolution**: Test actual DNS resolution after configuration
4. **Test Both DCs**: Verify both HDC1 and HDC2 configurations
5. **Check Connectivity**: Ensure nameservers are reachable
6. **Monitor Changes**: Watch for any network connectivity issues

## Expected Configuration Examples

### hcloud.healthgrp.com.sg (HDC1)
```
search hcloud.healthgrp.com.sg
# HDC1 AMK DNS
nameserver *************
nameserver *************
# HDC2 FORT DNS
nameserver *************
nameserver *************
```

### healthgrp.com.sg (HDC2)
```
search healthgrp.com.sg
# HDC2 FORT DNS
nameserver *************
nameserver *************
# HDC1 AMK DNS
nameserver *************
nameserver *************
```

## Security Considerations

- Script requires root privileges to modify system DNS configuration
- Validates FQDN and DC location parameters to prevent injection
- Creates backups before making changes
- Uses safe file operations with temporary files
- Preserves existing non-DNS configuration options
