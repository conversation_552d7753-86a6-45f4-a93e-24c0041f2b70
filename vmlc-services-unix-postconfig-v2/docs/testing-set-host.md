# Testing set-host.py Script Locally

This document explains how to test the `set-host.py` script directly on RedHat/Oracle Linux servers for validation before deployment.

## Overview

The `set-host.py` script configures:
- System hostname using `hostnamectl`
- `/etc/hosts` file with infrastructure entries
- NetBackup, RedHat Capsule, and EG Manager server entries

## Prerequisites

### System Requirements
- RedHat Enterprise Linux 7/8/9 or Oracle Linux 7/8/9
- Python 3.6 or higher
- Root or sudo privileges
- `hostnamectl` command available (systemd-based systems)

### Check Prerequisites
```bash
# Check Python version
python3 --version

# Check if hostnamectl is available
which hostnamectl

# Check current hostname
hostnamectl status

# Check sudo privileges
sudo -l
```

## Script Location and Permissions

```bash
# Copy the script to test location
cp /path/to/set-host.py /tmp/set-host.py

# Make executable
chmod +x /tmp/set-host.py

# Verify permissions
ls -la /tmp/set-host.py
```

## Usage Syntax

```bash
python3 /tmp/set-host.py <FQDN> <IP_ADDRESS>
```

### Parameters
- **FQDN**: Fully Qualified Domain Name (e.g., `server01.hcloud.healthgrp.com.sg`)
- **IP_ADDRESS**: IPv4 address for the server (e.g., `*************`)

## Testing Scenarios

### 1. Basic Hostname Configuration Test

```bash
# Test with valid FQDN and IP
sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] INFO: Starting hostname configuration: testserver.hcloud.healthgrp.com.sg (*************)
[2024-01-15T10:30:45.234567] INFO: Changing hostname from oldname to testserver.hcloud.healthgrp.com.sg
[2024-01-15T10:30:45.345678] INFO: Hostname successfully changed to: testserver.hcloud.healthgrp.com.sg
[2024-01-15T10:30:45.456789] INFO: Backed up /etc/hosts to /etc/hosts.backup.20240115_103045
[2024-01-15T10:30:45.567890] INFO: /etc/hosts file updated successfully with hostname: testserver.hcloud.healthgrp.com.sg and IP: *************
[2024-01-15T10:30:45.678901] INFO: Hostname and hosts file configuration completed successfully
```

### 2. Validation Tests

#### Test Invalid FQDN Format
```bash
sudo python3 /tmp/set-host.py "invalid..hostname" *************
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Invalid hostname format: invalid..hostname
```

#### Test Invalid IP Address
```bash
sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg 999.999.999.999
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Invalid IP address format: 999.999.999.999 (octet 999 > 255)
```

#### Test Missing Parameters
```bash
sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg
```

**Expected Output:**
```
[2024-01-15T10:30:45.123456] ERROR: Usage: python set-host.py <new_hostname> <ip_address>
```

### 3. Idempotency Test

```bash
# Run the same command twice
sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************
sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************
```

**Expected Output (Second Run):**
```
[2024-01-15T10:30:45.123456] INFO: Hostname is already set to testserver.hcloud.healthgrp.com.sg. No changes needed.
[2024-01-15T10:30:45.234567] INFO: /etc/hosts file updated successfully with hostname: testserver.hcloud.healthgrp.com.sg and IP: *************
```

## Verification Steps

### 1. Check Hostname Change
```bash
# Verify hostname was set correctly
hostnamectl status

# Check static hostname specifically
hostnamectl --static

# Verify FQDN resolution
hostname -f
```

### 2. Verify /etc/hosts File
```bash
# Check the hosts file content
cat /etc/hosts

# Verify your server entry
grep "$(hostname -s)" /etc/hosts

# Check for NetBackup entries
grep -i "netbackup\|backup" /etc/hosts

# Check for infrastructure entries
grep -i "capsule\|manager" /etc/hosts
```

### 3. Test Name Resolution
```bash
# Test local hostname resolution
nslookup $(hostname -f)

# Test reverse lookup
nslookup $(hostname -i)

# Ping test
ping -c 3 $(hostname -s)
```

## Backup and Recovery

### View Backup Files
```bash
# List backup files created
ls -la /etc/hosts.backup.*

# View backup content
cat /etc/hosts.backup.$(date +%Y%m%d)*
```

### Manual Recovery
```bash
# If you need to restore from backup
sudo cp /etc/hosts.backup.20240115_103045 /etc/hosts

# Restore original hostname (if needed)
sudo hostnamectl set-hostname original-hostname
```

## Advanced Testing

### 1. Test with Different Domain Types

```bash
# Test with hcloud domain
sudo python3 /tmp/set-host.py test01.hcloud.healthgrp.com.sg *************

# Test with healthgrp domain
sudo python3 /tmp/set-host.py test02.healthgrp.com.sg *************

# Test with staging domain
sudo python3 /tmp/set-host.py test03.devhealthgrp.com.sg *************
```

### 2. Test Error Conditions

```bash
# Test without sudo (should fail)
python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************

# Test with read-only filesystem (simulate)
sudo mount -o remount,ro /etc
sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************
sudo mount -o remount,rw /etc
```

### 3. Performance Testing

```bash
# Time the execution
time sudo python3 /tmp/set-host.py testserver.hcloud.healthgrp.com.sg *************
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Ensure script is executable
   chmod +x /tmp/set-host.py
   
   # Run with sudo
   sudo python3 /tmp/set-host.py ...
   ```

2. **Python Not Found**
   ```bash
   # Check Python installation
   which python3
   
   # Install if missing (RHEL/Oracle Linux)
   sudo yum install python3
   # or
   sudo dnf install python3
   ```

3. **hostnamectl Command Not Found**
   ```bash
   # Check systemd installation
   systemctl --version
   
   # For older systems, use hostname command
   hostname new-hostname
   ```

4. **Network Resolution Issues**
   ```bash
   # Check network configuration
   ip addr show
   
   # Verify DNS settings
   cat /etc/resolv.conf
   
   # Test connectivity
   ping -c 3 *******
   ```

## Integration Testing

### Test with Ansible Variables

```bash
# Simulate Ansible variable passing
FQDN="testserver.hcloud.healthgrp.com.sg"
PRD_IP="*************"

sudo python3 /tmp/set-host.py "$FQDN" "$PRD_IP"
```

### Test in Different Environments

```bash
# Production-like test
sudo python3 /tmp/set-host.py prod01.hcloud.healthgrp.com.sg ************

# Staging test
sudo python3 /tmp/set-host.py stg01.hcloud.healthgrp.com.sg ************

# Development test
sudo python3 /tmp/set-host.py dev01.devhealthgrp.com.sg ************
```

## Cleanup After Testing

```bash
# Remove test script
rm /tmp/set-host.py

# Restore original hostname if needed
sudo hostnamectl set-hostname original-hostname

# Restore original hosts file if needed
sudo cp /etc/hosts.backup.original /etc/hosts

# Clean up backup files (optional)
sudo rm /etc/hosts.backup.*
```

## Best Practices for Local Testing

1. **Always Backup**: The script creates automatic backups, but verify they exist
2. **Test in Stages**: Start with validation tests before actual configuration
3. **Use Test Hostnames**: Don't use production hostnames during testing
4. **Verify Changes**: Always check the results after running the script
5. **Document Results**: Keep notes of test results for troubleshooting
6. **Test Rollback**: Verify you can restore the original configuration

## Expected Files Modified

After successful execution, these files will be modified:
- `/etc/hostname` (indirectly via hostnamectl)
- `/etc/hosts` (directly by script)
- `/etc/hosts.backup.TIMESTAMP` (backup created by script)

## Security Considerations

- Script requires root privileges to modify system files
- Validates input parameters to prevent injection attacks
- Creates backups before making changes
- Uses safe file operations with temporary files
